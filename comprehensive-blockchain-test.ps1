# Comprehensive Blockchain Monitoring Integration Test
Write-Host "🧪 Qpesapay Blockchain Monitoring Integration Test" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green

$testUserId = "550e8400-e29b-41d4-a716-446655440000"
$headers = @{'Content-Type' = 'application/json'}

# Test 1: Create Bitcoin Wallet
Write-Host "`n📝 Test 1: Creating Bitcoin Wallet..." -ForegroundColor Yellow
$btcBody = "{`"user_id`": `"$testUserId`", `"currency`": `"BTC`"}"
try {
    $response = Invoke-WebRequest -Uri 'http://localhost:5003/wallets' -Method POST -Headers $headers -Body $btcBody -UseBasicParsing
    $btcWallet = $response.Content | ConvertFrom-Json
    Write-Host "✅ Bitcoin wallet created!" -ForegroundColor Green
    Write-Host "   Address: $($btcWallet.address)" -ForegroundColor Cyan
    Write-Host "   Wallet ID: $($btcWallet.id)" -ForegroundColor Cyan
} catch {
    if ($_.Exception.Message -like "*already exists*") {
        Write-Host "⚠️  Bitcoin wallet already exists - continuing..." -ForegroundColor Yellow
        # Get existing wallet
        $response = Invoke-WebRequest -Uri "http://localhost:5003/wallets/$testUserId" -UseBasicParsing
        $wallets = $response.Content | ConvertFrom-Json
        $btcWallet = $wallets | Where-Object { $_.currency -eq "BTC" } | Select-Object -First 1
        Write-Host "   Address: $($btcWallet.address)" -ForegroundColor Cyan
    } else {
        Write-Host "❌ Failed: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

# Test 2: Create Ethereum USDT Wallet
Write-Host "`n📝 Test 2: Creating Ethereum USDT Wallet..." -ForegroundColor Yellow
$ethBody = "{`"user_id`": `"$testUserId`", `"currency`": `"USDT_ERC20`"}"
try {
    $response = Invoke-WebRequest -Uri 'http://localhost:5003/wallets' -Method POST -Headers $headers -Body $ethBody -UseBasicParsing
    $ethWallet = $response.Content | ConvertFrom-Json
    Write-Host "✅ Ethereum USDT wallet created!" -ForegroundColor Green
    Write-Host "   Address: $($ethWallet.address)" -ForegroundColor Cyan
    Write-Host "   Wallet ID: $($ethWallet.id)" -ForegroundColor Cyan
} catch {
    if ($_.Exception.Message -like "*already exists*") {
        Write-Host "⚠️  Ethereum USDT wallet already exists - continuing..." -ForegroundColor Yellow
        $response = Invoke-WebRequest -Uri "http://localhost:5003/wallets/$testUserId" -UseBasicParsing
        $wallets = $response.Content | ConvertFrom-Json
        $ethWallet = $wallets | Where-Object { $_.currency -eq "USDT_ERC20" } | Select-Object -First 1
        Write-Host "   Address: $($ethWallet.address)" -ForegroundColor Cyan
    } else {
        Write-Host "❌ Failed: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

# Test 3: Create TRON USDT Wallet
Write-Host "`n📝 Test 3: Creating TRON USDT Wallet..." -ForegroundColor Yellow
$tronBody = "{`"user_id`": `"$testUserId`", `"currency`": `"USDT_TRC20`"}"
try {
    $response = Invoke-WebRequest -Uri 'http://localhost:5003/wallets' -Method POST -Headers $headers -Body $tronBody -UseBasicParsing
    $tronWallet = $response.Content | ConvertFrom-Json
    Write-Host "✅ TRON USDT wallet created!" -ForegroundColor Green
    Write-Host "   Address: $($tronWallet.address)" -ForegroundColor Cyan
    Write-Host "   Wallet ID: $($tronWallet.id)" -ForegroundColor Cyan
} catch {
    if ($_.Exception.Message -like "*already exists*") {
        Write-Host "⚠️  TRON USDT wallet already exists - continuing..." -ForegroundColor Yellow
        $response = Invoke-WebRequest -Uri "http://localhost:5003/wallets/$testUserId" -UseBasicParsing
        $wallets = $response.Content | ConvertFrom-Json
        $tronWallet = $wallets | Where-Object { $_.currency -eq "USDT_TRC20" } | Select-Object -First 1
        Write-Host "   Address: $($tronWallet.address)" -ForegroundColor Cyan
    } else {
        Write-Host "❌ Failed: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

# Test 4: Verify Blockchain Listener Health
Write-Host "`n📝 Test 4: Checking Blockchain Listener Health..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri 'http://localhost:5004/health' -UseBasicParsing
    $health = $response.Content | ConvertFrom-Json
    Write-Host "✅ Blockchain Listener is healthy!" -ForegroundColor Green
    Write-Host "   Status: $($health.status)" -ForegroundColor Cyan
    Write-Host "   Database: $($health.database)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ Blockchain Listener health check failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test 5: Monitor Blockchain Listener Activity
Write-Host "`n📝 Test 5: Monitoring Blockchain Listener Activity..." -ForegroundColor Yellow
Write-Host "   Checking recent blockchain monitoring logs..." -ForegroundColor Gray
try {
    $logs = docker logs qpesapay-blockchain-listener --tail 15 2>&1
    $bitcoinLogs = $logs | Where-Object { $_ -match "Bitcoin.*Processing" }
    $ethereumLogs = $logs | Where-Object { $_ -match "Ethereum.*Processing" }
    $tronLogs = $logs | Where-Object { $_ -match "TRON.*Processing" }
    
    Write-Host "✅ Blockchain monitoring is active:" -ForegroundColor Green
    if ($bitcoinLogs) { Write-Host "   🟡 Bitcoin: $(($bitcoinLogs | Select-Object -Last 1) -replace '.*INFO ', '')" -ForegroundColor Yellow }
    if ($ethereumLogs) { Write-Host "   🔵 Ethereum: $(($ethereumLogs | Select-Object -Last 1) -replace '.*INFO ', '')" -ForegroundColor Blue }
    if ($tronLogs) { Write-Host "   🔴 TRON: $(($tronLogs | Select-Object -Last 1) -replace '.*INFO ', '')" -ForegroundColor Red }
} catch {
    Write-Host "⚠️  Could not check blockchain listener logs" -ForegroundColor Yellow
}

# Test 6: Check Service Integration
Write-Host "`n📝 Test 6: Testing Service Integration..." -ForegroundColor Yellow
Write-Host "   Verifying all services are running and healthy..." -ForegroundColor Gray

$services = @(
    @{Name="Auth Service"; Url="http://localhost:5001/health"; Port="5001"},
    @{Name="Wallet Service"; Url="http://localhost:5003/health"; Port="5003"},
    @{Name="Blockchain Listener"; Url="http://localhost:5004/health"; Port="5004"}
)

foreach ($service in $services) {
    try {
        $response = Invoke-WebRequest -Uri $service.Url -UseBasicParsing -TimeoutSec 5
        Write-Host "   ✅ $($service.Name) (Port $($service.Port)): Healthy" -ForegroundColor Green
    } catch {
        Write-Host "   ❌ $($service.Name) (Port $($service.Port)): Unhealthy" -ForegroundColor Red
    }
}

Write-Host "`n🎉 Integration Test Summary" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green
Write-Host "✅ Multi-currency wallets created and ready for monitoring" -ForegroundColor White
Write-Host "✅ Blockchain listener is actively monitoring all three networks" -ForegroundColor White
Write-Host "✅ All services are healthy and communicating" -ForegroundColor White
Write-Host "`n📊 Created Wallet Addresses:" -ForegroundColor Yellow
if ($btcWallet) { Write-Host "   🟡 Bitcoin: $($btcWallet.address)" -ForegroundColor Yellow }
if ($ethWallet) { Write-Host "   🔵 Ethereum: $($ethWallet.address)" -ForegroundColor Blue }
if ($tronWallet) { Write-Host "   🔴 TRON: $($tronWallet.address)" -ForegroundColor Red }

Write-Host "`n💡 Next Steps:" -ForegroundColor Cyan
Write-Host "   - The blockchain listener is now monitoring these addresses" -ForegroundColor White
Write-Host "   - Any incoming transactions will be detected and processed" -ForegroundColor White
Write-Host "   - Wallet balances will be updated automatically" -ForegroundColor White
Write-Host "   - Notifications will be sent via webhooks" -ForegroundColor White
