use serde::Deserialize;
use std::env;
use crate::error::{AppError, Result};

#[derive(Debug, Clone, Deserialize)]
pub struct Config {
    pub database_url: String,
    pub port: u16,
    
    // YellowCard API configuration
    pub yellowcard_api_key: String,
    pub yellowcard_api_secret: String,
    pub yellowcard_base_url: String,
    pub yellowcard_webhook_secret: String,
    
    // M-Pesa configuration (via Africa's Talking)
    pub africas_talking_api_key: String,
    pub africas_talking_username: String,
    
    // Settlement configuration
    pub settlement_webhook_url: String,
    pub max_settlement_amount: String,
    pub min_settlement_amount: String,
    
    // Exchange rates
    pub exchange_rate_api_key: String,
    pub exchange_rate_update_interval: u64, // seconds
    
    // Processing configuration
    pub settlement_batch_size: usize,
    pub settlement_retry_attempts: u32,
    pub settlement_timeout_seconds: u64,
}

impl Config {
    pub fn from_env() -> Result<Self> {
        dotenv::dotenv().ok();

        Ok(Config {
            database_url: env::var("DATABASE_URL")
                .unwrap_or_else(|_| "postgres://qpesapay:qpesapay123@localhost:5432/qpesapay".to_string()),
            port: env::var("PORT")
                .unwrap_or_else(|_| "8000".to_string())
                .parse()
                .map_err(|e| AppError::Config(format!("Invalid port: {}", e)))?,
            
            // YellowCard configuration
            yellowcard_api_key: env::var("YELLOWCARD_API_KEY")
                .unwrap_or_else(|_| "mock-yellowcard-api-key".to_string()),
            yellowcard_api_secret: env::var("YELLOWCARD_API_SECRET")
                .unwrap_or_else(|_| "mock-yellowcard-api-secret".to_string()),
            yellowcard_base_url: env::var("YELLOWCARD_BASE_URL")
                .unwrap_or_else(|_| "https://api.yellowcard.io/v1".to_string()),
            yellowcard_webhook_secret: env::var("YELLOWCARD_WEBHOOK_SECRET")
                .unwrap_or_else(|_| "mock-webhook-secret".to_string()),
            
            // M-Pesa configuration
            africas_talking_api_key: env::var("AFRICAS_TALKING_API_KEY")
                .unwrap_or_else(|_| "mock-at-api-key".to_string()),
            africas_talking_username: env::var("AFRICAS_TALKING_USERNAME")
                .unwrap_or_else(|_| "sandbox".to_string()),
            
            // Settlement configuration
            settlement_webhook_url: env::var("SETTLEMENT_WEBHOOK_URL")
                .unwrap_or_else(|_| "http://localhost:5005/webhook/settlement".to_string()),
            max_settlement_amount: env::var("MAX_SETTLEMENT_AMOUNT")
                .unwrap_or_else(|_| "1000000.00".to_string()), // 1M KES
            min_settlement_amount: env::var("MIN_SETTLEMENT_AMOUNT")
                .unwrap_or_else(|_| "100.00".to_string()), // 100 KES
            
            // Exchange rates
            exchange_rate_api_key: env::var("EXCHANGE_RATE_API_KEY")
                .unwrap_or_else(|_| "mock-exchange-rate-key".to_string()),
            exchange_rate_update_interval: env::var("EXCHANGE_RATE_UPDATE_INTERVAL")
                .unwrap_or_else(|_| "300".to_string()) // 5 minutes
                .parse()
                .unwrap_or(300),
            
            // Processing configuration
            settlement_batch_size: env::var("SETTLEMENT_BATCH_SIZE")
                .unwrap_or_else(|_| "10".to_string())
                .parse()
                .unwrap_or(10),
            settlement_retry_attempts: env::var("SETTLEMENT_RETRY_ATTEMPTS")
                .unwrap_or_else(|_| "3".to_string())
                .parse()
                .unwrap_or(3),
            settlement_timeout_seconds: env::var("SETTLEMENT_TIMEOUT_SECONDS")
                .unwrap_or_else(|_| "30".to_string())
                .parse()
                .unwrap_or(30),
        })
    }
}
