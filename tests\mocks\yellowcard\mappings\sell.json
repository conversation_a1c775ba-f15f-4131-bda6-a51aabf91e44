{"request": {"method": "POST", "urlPath": "/api/v1/sell"}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "jsonBody": {"transaction_id": "{{randomValue type='UUID'}}", "status": "pending", "amount_crypto": "{{jsonPath request.body '$.amount'}}", "amount_kes": "{{multiply (jsonPath request.body '$.amount') 6450000}}", "exchange_rate": "6450000.00", "fee": "{{multiply (jsonPath request.body '$.amount') 32250}}", "recipient_phone": "{{jsonPath request.body '$.recipient_phone'}}", "created_at": "{{now}}", "estimated_completion": "{{now offset='5 minutes'}}"}}}