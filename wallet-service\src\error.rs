use axum::{
    http::StatusCode,
    response::{IntoResponse, Response},
    Json,
};
use serde_json::json;
use thiserror::Error;

pub type Result<T> = std::result::Result<T, AppError>;

#[derive(Error, Debug)]
pub enum AppError {
    #[error("Database error: {0}")]
    Database(#[from] sqlx::Error),

    #[error("Serialization error: {0}")]
    Serialization(#[from] serde_json::Error),

    #[error("Configuration error: {0}")]
    ConfigError(String),

    #[error("Cryptography error: {0}")]
    CryptoError(String),

    #[error("Wallet error: {0}")]
    WalletError(String),

    #[error("Blockchain error: {0}")]
    BlockchainError(String),

    #[error("Not found: {0}")]
    NotFound(String),

    #[error("Bad request: {0}")]
    BadRequest(String),

    #[error("Unauthorized: {0}")]
    Unauthorized(String),

    #[error("Internal server error: {0}")]
    InternalServerError(String),

    #[error("External API error: {0}")]
    ExternalApiError(String),
}

impl IntoResponse for AppError {
    fn into_response(self) -> Response {
        let (status, error_message) = match self {
            AppError::Database(ref err) => {
                tracing::error!("Database error: {}", err);
                (StatusCode::INTERNAL_SERVER_ERROR, "Database error occurred")
            }
            AppError::Serialization(ref err) => {
                tracing::error!("Serialization error: {}", err);
                (StatusCode::BAD_REQUEST, "Invalid request format")
            }
            AppError::ConfigError(ref msg) => {
                tracing::error!("Configuration error: {}", msg);
                (StatusCode::INTERNAL_SERVER_ERROR, "Configuration error")
            }
            AppError::CryptoError(ref msg) => {
                tracing::error!("Cryptography error: {}", msg);
                (StatusCode::INTERNAL_SERVER_ERROR, "Cryptography error")
            }
            AppError::WalletError(ref msg) => {
                tracing::warn!("Wallet error: {}", msg);
                (StatusCode::BAD_REQUEST, msg.as_str())
            }
            AppError::BlockchainError(ref msg) => {
                tracing::error!("Blockchain error: {}", msg);
                (StatusCode::INTERNAL_SERVER_ERROR, "Blockchain error")
            }
            AppError::NotFound(ref msg) => {
                tracing::warn!("Not found: {}", msg);
                (StatusCode::NOT_FOUND, msg.as_str())
            }
            AppError::BadRequest(ref msg) => {
                tracing::warn!("Bad request: {}", msg);
                (StatusCode::BAD_REQUEST, msg.as_str())
            }
            AppError::Unauthorized(ref msg) => {
                tracing::warn!("Unauthorized: {}", msg);
                (StatusCode::UNAUTHORIZED, msg.as_str())
            }
            AppError::InternalServerError(ref msg) => {
                tracing::error!("Internal server error: {}", msg);
                (StatusCode::INTERNAL_SERVER_ERROR, "Internal server error")
            }
            AppError::ExternalApiError(ref msg) => {
                tracing::error!("External API error: {}", msg);
                (StatusCode::BAD_GATEWAY, "External service error")
            }
        };

        let body = Json(json!({
            "error": error_message,
            "status": status.as_u16()
        }));

        (status, body).into_response()
    }
}
