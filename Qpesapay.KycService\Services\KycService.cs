using Microsoft.EntityFrameworkCore;
using Qpesapay.KycService.Data;
using Qpesapay.KycService.Models;

namespace Qpesapay.KycService.Services;

public interface IKycService
{
    Task<KycProfile?> GetProfileByUserIdAsync(Guid userId);
    Task<KycProfile> CreateProfileAsync(CreateKycProfileRequest request);
    Task<KycProfile?> UpdateProfileAsync(Guid profileId, UpdateKycProfileRequest request);
    Task<bool> UpdateVerificationStatusAsync(Guid profileId, string status, string? notes = null);
    Task<KycProfileResponse> MapToResponseAsync(KycProfile profile);
    Task<List<KycProfile>> GetProfilesByStatusAsync(string status);
}

public class KycService : IKycService
{
    private readonly KycDbContext _context;
    private readonly ILogger<KycService> _logger;

    public KycService(KycDbContext context, ILogger<KycService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<KycProfile?> GetProfileByUserIdAsync(Guid userId)
    {
        try
        {
            return await _context.Profiles
                .Include(p => p.Documents)
                .FirstOrDefaultAsync(p => p.UserId == userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving KYC profile for user: {UserId}", userId);
            throw;
        }
    }

    public async Task<KycProfile> CreateProfileAsync(CreateKycProfileRequest request)
    {
        try
        {
            // Check if profile already exists for this user
            var existingProfile = await GetProfileByUserIdAsync(request.UserId);
            if (existingProfile != null)
            {
                throw new InvalidOperationException("KYC profile already exists for this user");
            }

            // Validate required fields
            if (string.IsNullOrWhiteSpace(request.FirstName) || 
                string.IsNullOrWhiteSpace(request.LastName) ||
                string.IsNullOrWhiteSpace(request.IdNumber))
            {
                throw new ArgumentException("First name, last name, and ID number are required");
            }

            var profile = new KycProfile
            {
                UserId = request.UserId,
                FirstName = request.FirstName.Trim(),
                LastName = request.LastName.Trim(),
                DateOfBirth = request.DateOfBirth,
                Nationality = request.Nationality?.Trim(),
                IdNumber = request.IdNumber.Trim(),
                IdType = request.IdType?.ToLower(),
                Address = request.Address?.Trim(),
                City = request.City?.Trim(),
                Country = request.Country,
                VerificationStatus = "pending",
                VerificationLevel = 1,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _context.Profiles.Add(profile);
            await _context.SaveChangesAsync();

            _logger.LogInformation("KYC profile created for user: {UserId}", request.UserId);
            return profile;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating KYC profile for user: {UserId}", request.UserId);
            throw;
        }
    }

    public async Task<KycProfile?> UpdateProfileAsync(Guid profileId, UpdateKycProfileRequest request)
    {
        try
        {
            var profile = await _context.Profiles.FindAsync(profileId);
            if (profile == null)
            {
                return null;
            }

            // Update only provided fields
            if (!string.IsNullOrWhiteSpace(request.FirstName))
                profile.FirstName = request.FirstName.Trim();
            
            if (!string.IsNullOrWhiteSpace(request.LastName))
                profile.LastName = request.LastName.Trim();
            
            if (request.DateOfBirth.HasValue)
                profile.DateOfBirth = request.DateOfBirth.Value;
            
            if (!string.IsNullOrWhiteSpace(request.Nationality))
                profile.Nationality = request.Nationality.Trim();
            
            if (!string.IsNullOrWhiteSpace(request.IdNumber))
                profile.IdNumber = request.IdNumber.Trim();
            
            if (!string.IsNullOrWhiteSpace(request.IdType))
                profile.IdType = request.IdType.ToLower();
            
            if (!string.IsNullOrWhiteSpace(request.Address))
                profile.Address = request.Address.Trim();
            
            if (!string.IsNullOrWhiteSpace(request.City))
                profile.City = request.City.Trim();
            
            if (!string.IsNullOrWhiteSpace(request.Country))
                profile.Country = request.Country.Trim();

            profile.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("KYC profile updated: {ProfileId}", profileId);
            return profile;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating KYC profile: {ProfileId}", profileId);
            throw;
        }
    }

    public async Task<bool> UpdateVerificationStatusAsync(Guid profileId, string status, string? notes = null)
    {
        try
        {
            var profile = await _context.Profiles.FindAsync(profileId);
            if (profile == null)
            {
                return false;
            }

            // Validate status
            var validStatuses = new[] { "pending", "in_review", "verified", "rejected" };
            if (!validStatuses.Contains(status.ToLower()))
            {
                throw new ArgumentException("Invalid verification status");
            }

            profile.VerificationStatus = status.ToLower();
            profile.UpdatedAt = DateTime.UtcNow;

            // Update verification level based on status
            if (status.ToLower() == "verified")
            {
                profile.VerificationLevel = Math.Max(profile.VerificationLevel, 2);
            }

            await _context.SaveChangesAsync();

            _logger.LogInformation("KYC verification status updated: {ProfileId}, Status: {Status}", 
                profileId, status);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating verification status for profile: {ProfileId}", profileId);
            throw;
        }
    }

    public async Task<KycProfileResponse> MapToResponseAsync(KycProfile profile)
    {
        var documents = profile.Documents?.Select(d => new KycDocumentResponse(
            d.Id,
            d.DocumentType,
            d.FilePath,
            d.FileSize,
            d.MimeType,
            d.VerificationStatus,
            d.VerificationNotes,
            d.UploadedAt,
            d.VerifiedAt
        )).ToList() ?? new List<KycDocumentResponse>();

        return await Task.FromResult(new KycProfileResponse(
            profile.Id,
            profile.UserId,
            profile.FirstName,
            profile.LastName,
            profile.DateOfBirth,
            profile.Nationality,
            profile.IdNumber,
            profile.IdType,
            profile.Address,
            profile.City,
            profile.Country,
            profile.VerificationStatus,
            profile.VerificationLevel,
            profile.CreatedAt,
            profile.UpdatedAt,
            documents
        ));
    }

    public async Task<List<KycProfile>> GetProfilesByStatusAsync(string status)
    {
        try
        {
            return await _context.Profiles
                .Include(p => p.Documents)
                .Where(p => p.VerificationStatus == status.ToLower())
                .OrderBy(p => p.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving profiles by status: {Status}", status);
            throw;
        }
    }
}

// Extension methods for KycProfile entity
public static class KycProfileExtensions
{
    public static KycProfileResponse ToResponse(this KycProfile profile)
    {
        var documents = profile.Documents?.Select(d => new KycDocumentResponse(
            d.Id,
            d.DocumentType,
            d.FilePath,
            d.FileSize,
            d.MimeType,
            d.VerificationStatus,
            d.VerificationNotes,
            d.UploadedAt,
            d.VerifiedAt
        )).ToList() ?? new List<KycDocumentResponse>();

        return new KycProfileResponse(
            profile.Id,
            profile.UserId,
            profile.FirstName,
            profile.LastName,
            profile.DateOfBirth,
            profile.Nationality,
            profile.IdNumber,
            profile.IdType,
            profile.Address,
            profile.City,
            profile.Country,
            profile.VerificationStatus,
            profile.VerificationLevel,
            profile.CreatedAt,
            profile.UpdatedAt,
            documents
        );
    }
}
