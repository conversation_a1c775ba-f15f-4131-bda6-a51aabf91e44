use async_trait::async_trait;
use reqwest::Client;
use serde_json::Value;
use std::sync::Arc;
use tokio::time::{sleep, Duration};
use tracing::{info, error, warn, debug};

use crate::database::Database;
use crate::error::{AppError, Result};
use crate::models::{TronTransaction, BlockchainEvent, MonitoredAddress};
use crate::services::NotificationService;
use super::{BlockchainListener, ListenerStatus};

pub struct TronListener {
    http_client: Client,
    api_url: String,
    usdt_contract_address: String,
    database: Database,
    notification_service: Arc<NotificationService>,
    status: Arc<tokio::sync::RwLock<ListenerStatus>>,
    poll_interval: Duration,
}

impl TronListener {
    pub fn new(
        api_url: String,
        usdt_contract: String,
        database: Database,
        notification_service: Arc<NotificationService>,
    ) -> Self {
        let http_client = Client::new();
        
        let status = Arc::new(tokio::sync::RwLock::new(ListenerStatus {
            is_running: false,
            last_block_processed: None,
            last_error: None,
            processed_transactions: 0,
        }));

        Self {
            http_client,
            api_url,
            usdt_contract_address: usdt_contract,
            database,
            notification_service,
            status,
            poll_interval: Duration::from_secs(20), // TRON block time is ~3 seconds
        }
    }

    async fn get_monitored_addresses(&self) -> Result<Vec<MonitoredAddress>> {
        // Get all TRON addresses we need to monitor (TRX and USDT_TRC20)
        let mut addresses = self.database.get_monitored_addresses("TRX").await?;
        let usdt_addresses = self.database.get_monitored_addresses("USDT_TRC20").await?;
        addresses.extend(usdt_addresses);
        Ok(addresses)
    }

    async fn get_latest_block_number(&self) -> Result<u64> {
        let url = format!("{}/wallet/getnowblock", self.api_url);
        
        let response = self.http_client
            .post(&url)
            .send()
            .await
            .map_err(|e| AppError::Tron(format!("Failed to get latest block: {}", e)))?;

        let block: Value = response
            .json()
            .await
            .map_err(|e| AppError::Tron(format!("Failed to parse block response: {}", e)))?;

        let block_number = block["block_header"]["raw_data"]["number"]
            .as_u64()
            .ok_or_else(|| AppError::Tron("Invalid block number in response".to_string()))?;

        Ok(block_number)
    }

    async fn process_new_blocks(&self) -> Result<()> {
        let mut status = self.status.write().await;
        
        // Mock implementation - in production, get real block number
        let current_height = 50000000u64; // Mock TRON block height
        
        let start_height = status.last_block_processed
            .map(|h| h + 1)
            .unwrap_or(current_height.saturating_sub(10));

        info!("Processing TRON blocks from {} to {}", start_height, current_height);

        for height in start_height..=current_height {
            if let Err(e) = self.process_block(height).await {
                error!("Error processing TRON block {}: {}", height, e);
                status.last_error = Some(e.to_string());
                continue;
            }
            
            status.last_block_processed = Some(height);
            debug!("Processed TRON block {}", height);
        }

        Ok(())
    }

    async fn process_block(&self, height: u64) -> Result<()> {
        let monitored_addresses = self.get_monitored_addresses().await?;
        let address_set: std::collections::HashSet<String> = monitored_addresses
            .iter()
            .map(|addr| addr.address.clone())
            .collect();

        // Mock block processing - in production, fetch real block data
        info!("Processing TRON block {} (mock implementation)", height);
        
        // In a real implementation, you would:
        // 1. Fetch the block by number using /wallet/getblockbynum
        // 2. Check each transaction for our monitored addresses
        // 3. For USDT TRC20 transfers, check contract call transactions
        // 4. Store and notify about relevant transactions

        // Mock transaction for demonstration
        if height % 200 == 0 { // Every 200th block, simulate a transaction
            self.simulate_tron_transaction(height, &monitored_addresses).await?;
        }

        Ok(())
    }

    async fn simulate_tron_transaction(
        &self,
        block_height: u64,
        monitored_addresses: &[MonitoredAddress],
    ) -> Result<()> {
        if let Some(monitored) = monitored_addresses.first() {
            let tron_tx = TronTransaction {
                txid: format!("{:064x}", block_height * 1000), // Mock transaction ID
                from: "TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE".to_string(),
                to: monitored.address.clone(),
                amount: 1000000, // 1 TRX in sun (1 TRX = 1,000,000 sun)
                fee: 1000, // 0.001 TRX fee
                block_number: block_height,
                block_timestamp: chrono::Utc::now().timestamp() as u64,
                confirmations: 1,
                contract_address: if monitored.currency == "USDT_TRC20" {
                    Some(self.usdt_contract_address.clone())
                } else {
                    None
                },
            };

            let event = BlockchainEvent::TronTransaction(tron_tx);
            
            // Store transaction in database
            self.database.store_blockchain_transaction(&event, monitored).await?;
            
            // Send notification
            self.notification_service.send_transaction_notification(&event, monitored).await?;
            
            let mut status = self.status.write().await;
            status.processed_transactions += 1;
            
            info!("Simulated TRON transaction to address: {}", monitored.address);
        }

        Ok(())
    }

    async fn get_account_transactions(&self, address: &str) -> Result<Vec<Value>> {
        let url = format!("{}/v1/accounts/{}/transactions", self.api_url, address);
        
        let response = self.http_client
            .get(&url)
            .send()
            .await
            .map_err(|e| AppError::Tron(format!("Failed to get account transactions: {}", e)))?;

        let data: Value = response
            .json()
            .await
            .map_err(|e| AppError::Tron(format!("Failed to parse transactions response: {}", e)))?;

        let transactions = data["data"]
            .as_array()
            .unwrap_or(&vec![])
            .clone();

        Ok(transactions)
    }

    async fn process_usdt_trc20_transfers(&self, block_height: u64) -> Result<()> {
        // In production, this would:
        // 1. Get all transactions in the block
        // 2. Filter for contract calls to USDT TRC20 contract
        // 3. Decode the transfer function calls
        // 4. Check if recipient is in our monitored addresses
        // 5. Store and notify about USDT transfers

        info!("Processing USDT TRC20 transfers for block {} (mock)", block_height);
        Ok(())
    }
}

#[async_trait]
impl BlockchainListener for TronListener {
    async fn start(&self) -> Result<()> {
        info!("Starting TRON listener");
        
        {
            let mut status = self.status.write().await;
            status.is_running = true;
        }

        loop {
            if let Err(e) = self.process_new_blocks().await {
                error!("TRON listener error: {}", e);
                let mut status = self.status.write().await;
                status.last_error = Some(e.to_string());
            }

            sleep(self.poll_interval).await;
            
            // Check if we should continue running
            let status = self.status.read().await;
            if !status.is_running {
                break;
            }
        }

        info!("TRON listener stopped");
        Ok(())
    }

    async fn stop(&self) -> Result<()> {
        info!("Stopping TRON listener");
        let mut status = self.status.write().await;
        status.is_running = false;
        Ok(())
    }

    async fn get_status(&self) -> ListenerStatus {
        self.status.read().await.clone()
    }
}
