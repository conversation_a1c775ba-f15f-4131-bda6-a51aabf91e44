use std::sync::Arc;
use chrono::{Duration, Utc};
use rust_decimal::Decimal;
use tokio::time::{sleep, Duration as TokioDuration};
use tracing::{info, warn, error};
use uuid::Uuid;

use crate::config::Config;
use crate::database::Database;
use crate::error::{AppError, Result};
use crate::models::{
    Settlement, CreateSettlementRequest, SettlementResponse, SettlementJob,
    CryptoToKesConversion, ConversionFee,
};
use crate::services::{YellowcardService, ExchangeRateService, MpesaService};

#[derive(Clone)]
pub struct SettlementProcessor {
    db: Database,
    yellowcard_service: Arc<YellowcardService>,
    exchange_rate_service: Arc<ExchangeRateService>,
    mpesa_service: Arc<MpesaService>,
    config: Config,
}

impl SettlementProcessor {
    pub fn new(
        db: Database,
        yellowcard_service: Arc<YellowcardService>,
        config: Config,
    ) -> Self {
        let exchange_rate_service = Arc::new(ExchangeRateService::new(&config));
        let mpesa_service = Arc::new(MpesaService::new(&config));

        Self {
            db,
            yellowcard_service,
            exchange_rate_service,
            mpesa_service,
            config,
        }
    }

    pub async fn start(&self) -> Result<()> {
        info!("Starting settlement processor");

        // Start background tasks
        let processor = self.clone();
        let settlement_task = tokio::spawn(async move {
            processor.process_settlements_loop().await;
        });

        let processor = self.clone();
        let exchange_rate_task = tokio::spawn(async move {
            processor.update_exchange_rates_loop().await;
        });

        // Wait for tasks to complete (they run indefinitely)
        tokio::select! {
            _ = settlement_task => {
                error!("Settlement processing task stopped unexpectedly");
            }
            _ = exchange_rate_task => {
                error!("Exchange rate update task stopped unexpectedly");
            }
        }

        Ok(())
    }

    pub async fn create_settlement(
        &self,
        request: CreateSettlementRequest,
    ) -> Result<SettlementResponse> {
        info!("Creating settlement for user: {}", request.user_id);

        // Validate request
        self.validate_settlement_request(&request).await?;

        // Get current exchange rate
        let crypto_currency = self.normalize_currency(&request.crypto_currency);
        let exchange_rate = self
            .exchange_rate_service
            .get_current_rate(&crypto_currency, "KES")
            .await?;

        // Calculate conversion
        let crypto_amount = request.crypto_amount.parse::<Decimal>()
            .map_err(|e| AppError::InvalidAmount(format!("Invalid crypto amount: {}", e)))?;

        let conversion = self.calculate_conversion(
            crypto_amount,
            &crypto_currency,
            exchange_rate.rate,
        ).await?;

        // Create settlement record
        let settlement = Settlement {
            id: Uuid::new_v4(),
            user_id: request.user_id,
            transaction_id: request.transaction_id,
            crypto_amount,
            crypto_currency: crypto_currency.clone(),
            kes_amount: conversion.net_kes_amount,
            exchange_rate: exchange_rate.rate,
            recipient_phone: request.recipient_phone.clone(),
            recipient_name: request.recipient_name.clone(),
            status: "pending".to_string(),
            yellowcard_transaction_id: None,
            mpesa_transaction_id: None,
            failure_reason: None,
            webhook_url: request.webhook_url,
            created_at: Utc::now(),
            updated_at: Utc::now(),
            completed_at: None,
        };

        // Save to database
        let created_settlement = self.db.create_settlement(&settlement).await?;

        // Create settlement job for processing
        let job = SettlementJob {
            settlement_id: created_settlement.id,
            retry_count: 0,
            next_retry_at: Utc::now(),
            created_at: Utc::now(),
        };
        self.db.create_settlement_job(&job).await?;

        info!("Settlement created successfully: {}", created_settlement.id);
        Ok(created_settlement.into())
    }

    pub async fn get_exchange_rate(&self, from_currency: &str, to_currency: &str) -> Result<crate::models::ExchangeRate> {
        self.exchange_rate_service.get_current_rate(from_currency, to_currency).await
    }

    async fn process_settlements_loop(&self) {
        info!("Starting settlement processing loop");

        loop {
            match self.process_pending_settlements().await {
                Ok(processed_count) => {
                    if processed_count > 0 {
                        info!("Processed {} settlements", processed_count);
                    }
                }
                Err(e) => {
                    error!("Error processing settlements: {}", e);
                }
            }

            // Wait before next processing cycle
            sleep(TokioDuration::from_secs(30)).await;
        }
    }

    async fn process_pending_settlements(&self) -> Result<usize> {
        let jobs = self
            .db
            .get_ready_settlement_jobs(self.config.settlement_batch_size as i32)
            .await?;

        let mut processed_count = 0;

        for job in jobs {
            match self.process_single_settlement(job.settlement_id).await {
                Ok(_) => {
                    processed_count += 1;
                    self.db.delete_settlement_job(job.settlement_id).await?;
                }
                Err(e) => {
                    error!("Failed to process settlement {}: {}", job.settlement_id, e);
                    
                    // Handle retry logic
                    if job.retry_count < self.config.settlement_retry_attempts {
                        let next_retry = Utc::now() + Duration::minutes(5 * (job.retry_count + 1) as i64);
                        self.db
                            .update_settlement_job_retry(
                                job.settlement_id,
                                job.retry_count + 1,
                                next_retry,
                            )
                            .await?;
                    } else {
                        // Max retries reached, mark as failed
                        self.db
                            .update_settlement_status(
                                job.settlement_id,
                                "failed",
                                None,
                                None,
                                Some(format!("Max retries reached: {}", e)),
                            )
                            .await?;
                        self.db.delete_settlement_job(job.settlement_id).await?;
                    }
                }
            }
        }

        Ok(processed_count)
    }

    async fn process_single_settlement(&self, settlement_id: Uuid) -> Result<()> {
        info!("Processing settlement: {}", settlement_id);

        let settlement = self
            .db
            .get_settlement_by_id(settlement_id)
            .await?
            .ok_or_else(|| AppError::NotFound("Settlement not found".to_string()))?;

        if settlement.status != "pending" {
            return Ok(()); // Already processed
        }

        // Update status to processing
        self.db
            .update_settlement_status(settlement_id, "processing", None, None, None)
            .await?;

        // Process with YellowCard
        match self.process_with_yellowcard(&settlement).await {
            Ok(yellowcard_tx_id) => {
                info!(
                    "YellowCard transaction created: {} for settlement: {}",
                    yellowcard_tx_id, settlement_id
                );

                // Update with YellowCard transaction ID
                self.db
                    .update_settlement_status(
                        settlement_id,
                        "processing",
                        Some(yellowcard_tx_id),
                        None,
                        None,
                    )
                    .await?;

                // For mock implementation, immediately mark as completed
                self.complete_settlement_mock(settlement_id).await?;
            }
            Err(e) => {
                error!("YellowCard processing failed for {}: {}", settlement_id, e);
                return Err(e);
            }
        }

        Ok(())
    }

    async fn process_with_yellowcard(&self, settlement: &Settlement) -> Result<String> {
        info!("Processing settlement {} with YellowCard", settlement.id);

        // For mock implementation, return a mock transaction ID
        let mock_tx_id = format!("YC_{}", Uuid::new_v4().to_string()[..8].to_uppercase());
        
        info!("Mock YellowCard transaction created: {}", mock_tx_id);
        Ok(mock_tx_id)
    }

    async fn complete_settlement_mock(&self, settlement_id: Uuid) -> Result<()> {
        // Mock M-Pesa transaction ID
        let mock_mpesa_tx_id = format!("MP{}", chrono::Utc::now().timestamp());

        // Update settlement as completed
        self.db
            .update_settlement_status(
                settlement_id,
                "completed",
                None,
                Some(mock_mpesa_tx_id),
                None,
            )
            .await?;

        info!("Settlement {} completed successfully", settlement_id);
        Ok(())
    }

    async fn update_exchange_rates_loop(&self) {
        info!("Starting exchange rate update loop");

        loop {
            match self.exchange_rate_service.update_all_rates().await {
                Ok(_) => {
                    info!("Exchange rates updated successfully");
                }
                Err(e) => {
                    error!("Failed to update exchange rates: {}", e);
                }
            }

            // Wait for next update cycle
            sleep(TokioDuration::from_secs(self.config.exchange_rate_update_interval)).await;
        }
    }

    async fn validate_settlement_request(&self, request: &CreateSettlementRequest) -> Result<()> {
        // Validate crypto amount
        let amount = request.crypto_amount.parse::<Decimal>()
            .map_err(|e| AppError::InvalidAmount(format!("Invalid amount format: {}", e)))?;

        if amount <= Decimal::ZERO {
            return Err(AppError::InvalidAmount("Amount must be positive".to_string()));
        }

        // Validate currency
        let normalized_currency = self.normalize_currency(&request.crypto_currency);
        if !["BTC", "USDT"].contains(&normalized_currency.as_str()) {
            return Err(AppError::UnsupportedCurrency(format!(
                "Unsupported currency: {}",
                request.crypto_currency
            )));
        }

        // Validate phone number (basic validation)
        if !request.recipient_phone.starts_with("+254") {
            return Err(AppError::Validation(
                "Phone number must be in Kenyan format (+254...)".to_string(),
            ));
        }

        Ok(())
    }

    async fn calculate_conversion(
        &self,
        crypto_amount: Decimal,
        crypto_currency: &str,
        exchange_rate: Decimal,
    ) -> Result<CryptoToKesConversion> {
        let gross_kes_amount = crypto_amount * exchange_rate;

        // Calculate fees (mock implementation)
        let exchange_fee = gross_kes_amount * Decimal::new(25, 3); // 2.5%
        let processing_fee = Decimal::new(5000, 2); // 50 KES flat fee

        let fees = vec![
            ConversionFee {
                fee_type: "exchange".to_string(),
                amount: exchange_fee,
                currency: "KES".to_string(),
                description: "Exchange fee (2.5%)".to_string(),
            },
            ConversionFee {
                fee_type: "processing".to_string(),
                amount: processing_fee,
                currency: "KES".to_string(),
                description: "Processing fee".to_string(),
            },
        ];

        let total_fees = exchange_fee + processing_fee;
        let net_kes_amount = gross_kes_amount - total_fees;

        Ok(CryptoToKesConversion {
            crypto_amount,
            crypto_currency: crypto_currency.to_string(),
            kes_amount: gross_kes_amount,
            exchange_rate,
            fees,
            total_fees,
            net_kes_amount,
        })
    }

    fn normalize_currency(&self, currency: &str) -> String {
        match currency.to_uppercase().as_str() {
            "USDT_ERC20" | "USDT_TRC20" => "USDT".to_string(),
            other => other.to_string(),
        }
    }
}
