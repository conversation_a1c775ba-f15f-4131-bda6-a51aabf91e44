use async_trait::async_trait;
use std::sync::Arc;
use tokio::time::{sleep, Duration};
use tracing::{info, error, warn, debug};

use crate::database::Database;
use crate::error::{AppError, Result};
use crate::models::{EthereumTransaction, EthereumLog, BlockchainEvent, MonitoredAddress};
use crate::services::NotificationService;
use super::{BlockchainListener, ListenerStatus};

pub struct EthereumListener {
    ws_url: String,
    usdt_contract_address: String,
    database: Database,
    notification_service: Arc<NotificationService>,
    status: Arc<tokio::sync::RwLock<ListenerStatus>>,
    poll_interval: Duration,
}

impl EthereumListener {
    pub fn new(
        _ws_url: String,
        usdt_contract: String,
        database: Database,
        notification_service: Arc<NotificationService>,
    ) -> Self {
        let status = Arc::new(tokio::sync::RwLock::new(ListenerStatus {
            is_running: false,
            last_block_processed: None,
            last_error: None,
            processed_transactions: 0,
        }));

        Self {
            ws_url: _ws_url,
            usdt_contract_address: usdt_contract,
            database,
            notification_service,
            status,
            poll_interval: Duration::from_secs(15),
        }
    }

    async fn get_monitored_addresses(&self) -> Result<Vec<MonitoredAddress>> {
        // Get all Ethereum addresses we need to monitor (ETH and USDT_ERC20)
        let mut addresses = self.database.get_monitored_addresses("ETH").await?;
        let usdt_addresses = self.database.get_monitored_addresses("USDT_ERC20").await?;
        addresses.extend(usdt_addresses);
        Ok(addresses)
    }

    async fn process_new_blocks(&self) -> Result<()> {
        let mut status = self.status.write().await;
        
        // Mock implementation - in production, get real block number
        let current_height = 1000000u64; // Mock block height
        
        let start_height = status.last_block_processed
            .map(|h| h + 1)
            .unwrap_or(current_height.saturating_sub(10));

        info!("Processing Ethereum blocks from {} to {}", start_height, current_height);

        for height in start_height..=current_height {
            if let Err(e) = self.process_block(height).await {
                error!("Error processing Ethereum block {}: {}", height, e);
                status.last_error = Some(e.to_string());
                continue;
            }
            
            status.last_block_processed = Some(height);
            debug!("Processed Ethereum block {}", height);
        }

        Ok(())
    }

    async fn process_block(&self, height: u64) -> Result<()> {
        let monitored_addresses = self.get_monitored_addresses().await?;
        let address_set: std::collections::HashSet<String> = monitored_addresses
            .iter()
            .map(|addr| addr.address.to_lowercase())
            .collect();

        // Mock block processing - in production, fetch real block data
        info!("Processing Ethereum block {} (mock implementation)", height);
        
        // In a real implementation, you would:
        // 1. Fetch the block and all its transactions
        // 2. Check each transaction for our monitored addresses
        // 3. For USDT transfers, decode the Transfer event logs
        // 4. Store and notify about relevant transactions

        // Mock transaction for demonstration
        if height % 100 == 0 { // Every 100th block, simulate a transaction
            self.simulate_ethereum_transaction(height, &monitored_addresses).await?;
        }

        Ok(())
    }

    async fn simulate_ethereum_transaction(
        &self,
        block_height: u64,
        monitored_addresses: &[MonitoredAddress],
    ) -> Result<()> {
        if let Some(monitored) = monitored_addresses.first() {
            let eth_tx = EthereumTransaction {
                hash: format!("0x{:064x}", block_height), // Mock hash
                from: "******************************************".to_string(),
                to: monitored.address.clone(),
                value: "1000000000000000000".to_string(), // 1 ETH in wei
                gas: "21000".to_string(),
                gas_price: "20000000000".to_string(), // 20 gwei
                gas_used: Some("21000".to_string()),
                nonce: 1,
                block_number: Some(block_height),
                block_hash: Some(format!("0x{:064x}", block_height + 1000000)),
                transaction_index: Some(0),
                confirmations: 1,
                timestamp: Some(chrono::Utc::now()),
            };

            let event = BlockchainEvent::EthereumTransaction(eth_tx);
            
            // Store transaction in database
            self.database.store_blockchain_transaction(&event, monitored).await?;
            
            // Send notification
            self.notification_service.send_transaction_notification(&event, monitored).await?;
            
            let mut status = self.status.write().await;
            status.processed_transactions += 1;
            
            info!("Simulated Ethereum transaction to address: {}", monitored.address);
        }

        Ok(())
    }

    async fn process_usdt_transfer_logs(&self, block_height: u64) -> Result<()> {
        // In production, this would:
        // 1. Filter logs for USDT Transfer events
        // 2. Decode the Transfer(address,address,uint256) event
        // 3. Check if 'to' address is in our monitored addresses
        // 4. Store and notify about USDT transfers

        info!("Processing USDT transfer logs for block {} (mock)", block_height);
        Ok(())
    }
}

#[async_trait]
impl BlockchainListener for EthereumListener {
    async fn start(&self) -> Result<()> {
        info!("Starting Ethereum listener");
        
        {
            let mut status = self.status.write().await;
            status.is_running = true;
        }

        loop {
            if let Err(e) = self.process_new_blocks().await {
                error!("Ethereum listener error: {}", e);
                let mut status = self.status.write().await;
                status.last_error = Some(e.to_string());
            }

            sleep(self.poll_interval).await;
            
            // Check if we should continue running
            let status = self.status.read().await;
            if !status.is_running {
                break;
            }
        }

        info!("Ethereum listener stopped");
        Ok(())
    }

    async fn stop(&self) -> Result<()> {
        info!("Stopping Ethereum listener");
        let mut status = self.status.write().await;
        status.is_running = false;
        Ok(())
    }

    async fn get_status(&self) -> ListenerStatus {
        self.status.read().await.clone()
    }
}
