[tool.poetry]
name = "integuru"
version = "0.1.0"
description = ""
authors = ["al<PERSON><PERSON><PERSON> <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = ">=3.12,<3.14"
langchain-openai = "^0.2.0"
langchain-core = "^0.3.1"
langgraph = "^0.2.22"
langsmith = "^0.1.122"
python-dotenv = "^1.0.1"
click = "^8.1.7"
playwright = "^1.47.0"
networkx = "^3.3"
matplotlib = "^3.9.2"
ipykernel = "^6.29.5"

[tool.poetry.scripts]
integuru = "integuru.__main__:cli"

[tool.poetry.dev-dependencies]
pytest = "^7.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
