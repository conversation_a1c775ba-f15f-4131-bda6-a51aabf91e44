using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using FluentAssertions;
using Xunit;
using Qpesapay.AuthService.Data;
using Qpesapay.AuthService.Services;

namespace Qpesapay.AuthService.Tests.Services;

public class OtpServiceTests : IDisposable
{
    private readonly AuthDbContext _context;
    private readonly Mock<IHttpClientFactory> _httpClientFactoryMock;
    private readonly Mock<IConfiguration> _configurationMock;
    private readonly Mock<ILogger<OtpService>> _loggerMock;
    private readonly OtpService _otpService;

    public OtpServiceTests()
    {
        var options = new DbContextOptionsBuilder<AuthDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new AuthDbContext(options);
        _httpClientFactoryMock = new Mock<IHttpClientFactory>();
        _configurationMock = new Mock<IConfiguration>();
        _loggerMock = new Mock<ILogger<OtpService>>();

        // Setup configuration
        _configurationMock.Setup(c => c["OtpSettings:Provider"]).Returns("test");
        _configurationMock.Setup(c => c["OtpSettings:ApiKey"]).Returns("test-api-key");
        _configurationMock.Setup(c => c["OtpSettings:Username"]).Returns("test-username");

        _otpService = new OtpService(
            _httpClientFactoryMock.Object,
            _configurationMock.Object,
            _context,
            _loggerMock.Object
        );
    }

    [Fact]
    public async Task GenerateAndStoreOtpAsync_ValidRequest_ShouldGenerateOtp()
    {
        // Arrange
        var email = "<EMAIL>";
        var purpose = "signup";
        var userId = Guid.NewGuid();

        // Act
        var otpCode = await _otpService.GenerateAndStoreOtpAsync(email, purpose, userId);

        // Assert
        otpCode.Should().NotBeNullOrEmpty();
        otpCode.Should().HaveLength(6);
        otpCode.Should().MatchRegex(@"^\d{6}$");

        // Verify OTP is stored in database
        var storedOtp = await _context.OtpCodes
            .FirstOrDefaultAsync(o => o.Email == email && o.Purpose == purpose);

        storedOtp.Should().NotBeNull();
        storedOtp!.Code.Should().Be(otpCode);
        storedOtp.UserId.Should().Be(userId);
        storedOtp.ExpiresAt.Should().BeAfter(DateTime.UtcNow);
    }

    [Fact]
    public async Task GenerateAndStoreOtpAsync_ExistingUnusedOtp_ShouldReplaceOldOtp()
    {
        // Arrange
        var email = "<EMAIL>";
        var purpose = "signup";
        var userId = Guid.NewGuid();

        // Generate first OTP
        var firstOtp = await _otpService.GenerateAndStoreOtpAsync(email, purpose, userId);

        // Act - Generate second OTP
        var secondOtp = await _otpService.GenerateAndStoreOtpAsync(email, purpose, userId);

        // Assert
        firstOtp.Should().NotBe(secondOtp);

        // Verify only one OTP exists for this email/purpose
        var otpCount = await _context.OtpCodes
            .CountAsync(o => o.Email == email && o.Purpose == purpose && o.UsedAt == null);

        otpCount.Should().Be(1);

        var storedOtp = await _context.OtpCodes
            .FirstOrDefaultAsync(o => o.Email == email && o.Purpose == purpose && o.UsedAt == null);

        storedOtp!.Code.Should().Be(secondOtp);
    }

    [Fact]
    public async Task ValidateOtpAsync_ValidOtp_ShouldReturnTrue()
    {
        // Arrange
        var email = "<EMAIL>";
        var purpose = "signup";
        var userId = Guid.NewGuid();

        var otpCode = await _otpService.GenerateAndStoreOtpAsync(email, purpose, userId);

        // Act
        var result = await _otpService.ValidateOtpAsync(email, otpCode, purpose);

        // Assert
        result.Should().BeTrue();

        // Verify OTP is marked as used
        var storedOtp = await _context.OtpCodes
            .FirstOrDefaultAsync(o => o.Email == email && o.Purpose == purpose);

        storedOtp!.UsedAt.Should().NotBeNull();
        storedOtp.UsedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
    }

    [Fact]
    public async Task ValidateOtpAsync_InvalidOtp_ShouldReturnFalse()
    {
        // Arrange
        var email = "<EMAIL>";
        var purpose = "signup";
        var userId = Guid.NewGuid();

        await _otpService.GenerateAndStoreOtpAsync(email, purpose, userId);

        // Act
        var result = await _otpService.ValidateOtpAsync(email, "999999", purpose);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task ValidateOtpAsync_ExpiredOtp_ShouldReturnFalse()
    {
        // Arrange
        var email = "<EMAIL>";
        var purpose = "signup";
        var userId = Guid.NewGuid();

        var otpCode = await _otpService.GenerateAndStoreOtpAsync(email, purpose, userId);

        // Manually expire the OTP
        var storedOtp = await _context.OtpCodes
            .FirstOrDefaultAsync(o => o.Email == email && o.Purpose == purpose);

        storedOtp!.ExpiresAt = DateTime.UtcNow.AddMinutes(-1);
        await _context.SaveChangesAsync();

        // Act
        var result = await _otpService.ValidateOtpAsync(email, otpCode, purpose);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task ValidateOtpAsync_AlreadyUsedOtp_ShouldReturnFalse()
    {
        // Arrange
        var email = "<EMAIL>";
        var purpose = "signup";
        var userId = Guid.NewGuid();

        var otpCode = await _otpService.GenerateAndStoreOtpAsync(email, purpose, userId);

        // Use the OTP once
        await _otpService.ValidateOtpAsync(email, otpCode, purpose);

        // Act - Try to use the same OTP again
        var result = await _otpService.ValidateOtpAsync(email, otpCode, purpose);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task CleanupExpiredOtpsAsync_ShouldRemoveExpiredOtps()
    {
        // Arrange
        var email1 = "<EMAIL>";
        var email2 = "<EMAIL>";
        var purpose = "signup";

        // Generate OTPs
        await _otpService.GenerateAndStoreOtpAsync(email1, purpose);
        await _otpService.GenerateAndStoreOtpAsync(email2, purpose);

        // Manually expire one OTP
        var expiredOtp = await _context.OtpCodes
            .FirstOrDefaultAsync(o => o.Email == email1);

        expiredOtp!.ExpiresAt = DateTime.UtcNow.AddMinutes(-1);
        await _context.SaveChangesAsync();

        // Act
        await _otpService.CleanupExpiredOtpsAsync();

        // Assert
        var remainingOtps = await _context.OtpCodes.ToListAsync();
        remainingOtps.Should().HaveCount(1);
        remainingOtps[0].Email.Should().Be(email2);
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
