use aes_gcm::{
    aead::{<PERSON><PERSON>, KeyInit},
    Aes256Gcm, <PERSON>ce,
};
use base58::ToBase58;
use bip39::{Language, Mnemonic};
use bitcoin::{Network, PrivateKey, PublicKey};
use rand::{Rng, RngCore};
use secp256k1::{Secp256k1, SecretKey};
use sha2::{Digest, Sha256};
use std::str::FromStr;

use crate::error::{AppError, Result};
use crate::models::{Currency, HDWallet, KeyPair};

pub struct CryptoService {
    cipher: Aes256Gcm,
}

impl CryptoService {
    pub fn new(encryption_key: &str) -> Result<Self> {
        // Derive a 32-byte key from the provided key
        let mut hasher = Sha256::new();
        hasher.update(encryption_key.as_bytes());
        let key = hasher.finalize();

        let cipher = Aes256Gcm::new_from_slice(&key)
            .map_err(|e| AppError::CryptoError(format!("Failed to create cipher: {}", e)))?;

        Ok(CryptoService { cipher })
    }

    pub fn generate_mnemonic(&self) -> Result<String> {
        // Generate random entropy for mnemonic
        let mut entropy = [0u8; 16]; // 128 bits for 12 words
        rand::thread_rng().fill(&mut entropy);

        let mnemonic = Mnemonic::from_entropy(&entropy)
            .map_err(|e| AppError::CryptoError(format!("Failed to generate mnemonic: {}", e)))?;

        Ok(mnemonic.to_string())
    }

    pub fn create_hd_wallet(&self, mnemonic: Option<String>) -> Result<HDWallet> {
        let mnemonic_str = match mnemonic {
            Some(m) => m,
            None => self.generate_mnemonic()?,
        };

        let mnemonic = Mnemonic::from_str(&mnemonic_str)
            .map_err(|e| AppError::CryptoError(format!("Invalid mnemonic: {}", e)))?;

        let seed = mnemonic.to_seed("");
        let seed_bytes = seed.to_vec();

        // For now, use a simplified master key representation
        let master_key = hex::encode(&seed_bytes[..32]);

        Ok(HDWallet {
            mnemonic: mnemonic_str,
            seed: seed_bytes,
            master_key,
        })
    }

    pub fn derive_keypair(&self, hd_wallet: &HDWallet, currency: &Currency, account: u32, index: u32) -> Result<KeyPair> {
        match currency {
            Currency::Bitcoin => self.derive_bitcoin_keypair(hd_wallet, account, index),
            Currency::UsdtErc20 => self.derive_ethereum_keypair(hd_wallet, account, index),
            Currency::UsdtTrc20 => self.derive_tron_keypair(hd_wallet, account, index),
        }
    }

    fn derive_bitcoin_keypair(&self, hd_wallet: &HDWallet, account: u32, index: u32) -> Result<KeyPair> {
        let secp = Secp256k1::new();
        
        // BIP44 derivation path: m/44'/0'/account'/0/index (Bitcoin mainnet)
        // For testnet: m/44'/1'/account'/0/index
        let derivation_path = format!("m/44'/1'/{}'/{}/{}", account, 0, index); // Using testnet

        // Simplified key derivation - use master key + derivation path hash
        let mut hasher = Sha256::new();
        hasher.update(&hd_wallet.master_key);
        hasher.update(derivation_path.as_bytes());
        let derived_bytes = hasher.finalize();

        let secret_key = SecretKey::from_slice(&derived_bytes[..])
            .map_err(|e| AppError::CryptoError(format!("Invalid secret key: {}", e)))?;
        let private_key = PrivateKey::new(secret_key, Network::Testnet);
        let public_key = PublicKey::from_private_key(&secp, &private_key);
        
        // Generate P2PKH address
        let address = bitcoin::Address::p2pkh(&public_key, Network::Testnet)
            .to_string();

        Ok(KeyPair {
            private_key: private_key.to_wif(),
            public_key: public_key.to_string(),
            address,
        })
    }

    fn derive_ethereum_keypair(&self, hd_wallet: &HDWallet, account: u32, index: u32) -> Result<KeyPair> {
        let secp = Secp256k1::new();
        
        // BIP44 derivation path for Ethereum: m/44'/60'/account'/0/index
        let derivation_path = format!("m/44'/60'/{}'/{}/{}", account, 0, index);

        // Simplified key derivation
        let mut hasher = Sha256::new();
        hasher.update(&hd_wallet.master_key);
        hasher.update(derivation_path.as_bytes());
        let derived_bytes = hasher.finalize();

        let private_key_hex = hex::encode(&derived_bytes[..]);

        // Generate Ethereum address from public key
        let secret_key = SecretKey::from_slice(&derived_bytes[..])
            .map_err(|e| AppError::CryptoError(format!("Invalid secret key: {}", e)))?;
        
        let public_key = secp256k1::PublicKey::from_secret_key(&secp, &secret_key);
        let public_key_bytes = public_key.serialize_uncompressed();
        
        // Ethereum address is the last 20 bytes of Keccak256 hash of public key (without 0x04 prefix)
        let mut hasher = sha3::Keccak256::new();
        hasher.update(&public_key_bytes[1..]);
        let hash = hasher.finalize();
        let address = format!("0x{}", hex::encode(&hash[12..]));

        Ok(KeyPair {
            private_key: format!("0x{}", private_key_hex),
            public_key: hex::encode(public_key_bytes),
            address,
        })
    }

    fn derive_tron_keypair(&self, hd_wallet: &HDWallet, account: u32, index: u32) -> Result<KeyPair> {
        let secp = Secp256k1::new();
        
        // BIP44 derivation path for TRON: m/44'/195'/account'/0/index
        let derivation_path = format!("m/44'/195'/{}'/{}/{}", account, 0, index);

        // Simplified key derivation
        let mut hasher = Sha256::new();
        hasher.update(&hd_wallet.master_key);
        hasher.update(derivation_path.as_bytes());
        let derived_bytes = hasher.finalize();

        let private_key_hex = hex::encode(&derived_bytes[..]);

        let secret_key = SecretKey::from_slice(&derived_bytes[..])
            .map_err(|e| AppError::CryptoError(format!("Invalid secret key: {}", e)))?;
        
        let public_key = secp256k1::PublicKey::from_secret_key(&secp, &secret_key);
        let public_key_bytes = public_key.serialize_uncompressed();

        // TRON address generation
        let mut hasher = sha3::Keccak256::new();
        hasher.update(&public_key_bytes[1..]);
        let hash = hasher.finalize();
        
        // Add TRON address prefix (0x41)
        let mut address_bytes = vec![0x41];
        address_bytes.extend_from_slice(&hash[12..]);
        
        // Calculate checksum
        let mut checksum_hasher = Sha256::new();
        checksum_hasher.update(&address_bytes);
        let checksum1 = checksum_hasher.finalize();
        
        let mut checksum_hasher2 = Sha256::new();
        checksum_hasher2.update(&checksum1);
        let checksum2 = checksum_hasher2.finalize();
        
        address_bytes.extend_from_slice(&checksum2[..4]);
        
        let address = address_bytes.to_base58();

        Ok(KeyPair {
            private_key: private_key_hex,
            public_key: hex::encode(public_key_bytes),
            address,
        })
    }

    pub fn encrypt_data(&self, data: &str) -> Result<String> {
        let nonce_bytes: [u8; 12] = rand::thread_rng().gen();
        let nonce = Nonce::from_slice(&nonce_bytes);

        let ciphertext = self.cipher
            .encrypt(nonce, data.as_bytes())
            .map_err(|e| AppError::CryptoError(format!("Encryption failed: {}", e)))?;

        // Combine nonce and ciphertext
        let mut result = nonce_bytes.to_vec();
        result.extend_from_slice(&ciphertext);

        Ok(hex::encode(result))
    }

    pub fn decrypt_data(&self, encrypted_data: &str) -> Result<String> {
        let data = hex::decode(encrypted_data)
            .map_err(|e| AppError::CryptoError(format!("Invalid hex data: {}", e)))?;

        if data.len() < 12 {
            return Err(AppError::CryptoError("Invalid encrypted data length".to_string()));
        }

        let (nonce_bytes, ciphertext) = data.split_at(12);
        let nonce = Nonce::from_slice(nonce_bytes);

        let plaintext = self.cipher
            .decrypt(nonce, ciphertext)
            .map_err(|e| AppError::CryptoError(format!("Decryption failed: {}", e)))?;

        String::from_utf8(plaintext)
            .map_err(|e| AppError::CryptoError(format!("Invalid UTF-8: {}", e)))
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_generate_mnemonic() {
        let crypto_service = CryptoService::new("test-encryption-key-32-chars-long").unwrap();

        let mnemonic = crypto_service.generate_mnemonic().unwrap();

        // Should be 12 words separated by spaces
        let words: Vec<&str> = mnemonic.split_whitespace().collect();
        assert_eq!(words.len(), 12);

        // Should be valid BIP39 mnemonic
        assert!(Mnemonic::from_str(&mnemonic).is_ok());
    }

    #[test]
    fn test_create_hd_wallet() {
        let crypto_service = CryptoService::new("test-encryption-key-32-chars-long").unwrap();

        let hd_wallet = crypto_service.create_hd_wallet(None).unwrap();

        assert!(!hd_wallet.mnemonic.is_empty());
        assert!(!hd_wallet.seed.is_empty());
        assert!(!hd_wallet.master_key.is_empty());

        // Verify mnemonic is valid
        assert!(Mnemonic::from_str(&hd_wallet.mnemonic).is_ok());
    }

    #[test]
    fn test_encrypt_decrypt_data() {
        let crypto_service = CryptoService::new("test-encryption-key-32-chars-long").unwrap();
        let test_data = "sensitive private key data";

        let encrypted = crypto_service.encrypt_data(test_data).unwrap();
        let decrypted = crypto_service.decrypt_data(&encrypted).unwrap();

        assert_ne!(encrypted, test_data);
        assert_eq!(decrypted, test_data);
    }

    #[test]
    fn test_currency_from_string() {
        assert_eq!(Currency::from_string("BTC"), Some(Currency::Bitcoin));
        assert_eq!(Currency::from_string("btc"), Some(Currency::Bitcoin));
        assert_eq!(Currency::from_string("USDT_ERC20"), Some(Currency::UsdtErc20));
        assert_eq!(Currency::from_string("invalid"), None);
    }
}
