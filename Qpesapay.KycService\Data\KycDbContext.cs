using Microsoft.EntityFrameworkCore;
using Qpesapay.KycService.Models;

namespace Qpesapay.KycService.Data;

public class KycDbContext : DbContext
{
    public KycDbContext(DbContextOptions<KycDbContext> options) : base(options)
    {
    }

    public DbSet<KycProfile> Profiles { get; set; }
    public DbSet<KycDocument> Documents { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure KycProfile entity
        modelBuilder.Entity<KycProfile>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.UserId);
            entity.HasIndex(e => e.VerificationStatus);
            
            entity.Property(e => e.FirstName).HasMaxLength(100);
            entity.Property(e => e.LastName).HasMaxLength(100);
            entity.Property(e => e.Nationality).HasMaxLength(50);
            entity.Property(e => e.IdNumber).HasMaxLength(50);
            entity.Property(e => e.IdType).HasMaxLength(20);
            entity.Property(e => e.City).HasMaxLength(100);
            entity.Property(e => e.Country).HasMaxLength(50);
            entity.Property(e => e.VerificationStatus).HasMaxLength(20);
            
            // Check constraints
            entity.HasCheckConstraint("CK_KycProfile_VerificationStatus", 
                "verification_status IN ('pending', 'in_review', 'verified', 'rejected')");
            entity.HasCheckConstraint("CK_KycProfile_VerificationLevel", 
                "verification_level BETWEEN 1 AND 3");
            entity.HasCheckConstraint("CK_KycProfile_IdType", 
                "id_type IN ('national_id', 'passport', 'driving_license')");
        });

        // Configure KycDocument entity
        modelBuilder.Entity<KycDocument>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.ProfileId);
            
            entity.Property(e => e.DocumentType).IsRequired().HasMaxLength(50);
            entity.Property(e => e.FilePath).IsRequired().HasMaxLength(500);
            entity.Property(e => e.MimeType).HasMaxLength(100);
            entity.Property(e => e.VerificationStatus).HasMaxLength(20);
            
            // Foreign key relationship
            entity.HasOne(e => e.Profile)
                  .WithMany(p => p.Documents)
                  .HasForeignKey(e => e.ProfileId)
                  .OnDelete(DeleteBehavior.Cascade);
                  
            // Check constraints
            entity.HasCheckConstraint("CK_KycDocument_DocumentType", 
                "document_type IN ('id_front', 'id_back', 'selfie', 'proof_of_address')");
            entity.HasCheckConstraint("CK_KycDocument_VerificationStatus", 
                "verification_status IN ('pending', 'verified', 'rejected')");
        });

        // Configure timestamp columns to use UTC
        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            foreach (var property in entityType.GetProperties())
            {
                if (property.ClrType == typeof(DateTime) || property.ClrType == typeof(DateTime?))
                {
                    property.SetColumnType("timestamp with time zone");
                }
            }
        }
    }

    public override int SaveChanges()
    {
        UpdateTimestamps();
        return base.SaveChanges();
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        UpdateTimestamps();
        return await base.SaveChangesAsync(cancellationToken);
    }

    private void UpdateTimestamps()
    {
        var entries = ChangeTracker.Entries()
            .Where(e => e.Entity is KycProfile && e.State == EntityState.Modified);

        foreach (var entry in entries)
        {
            if (entry.Entity is KycProfile profile)
            {
                profile.UpdatedAt = DateTime.UtcNow;
            }
        }
    }
}
