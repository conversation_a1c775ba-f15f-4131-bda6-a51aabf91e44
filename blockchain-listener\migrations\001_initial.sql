-- Blockchain Listener Service Database Schema
-- This extends the existing wallet service schema

-- Create blockchain_listener schema for service-specific tables
CREATE SCHEMA IF NOT EXISTS blockchain_listener;

-- Table to track the last processed block for each blockchain
CREATE TABLE IF NOT EXISTS blockchain_listener.block_checkpoints (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    blockchain VARCHAR(20) NOT NULL UNIQUE, -- 'bitcoin', 'ethereum', 'tron'
    last_processed_block BIGINT NOT NULL DEFAULT 0,
    last_processed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table to track monitored addresses and their status
CREATE TABLE IF NOT EXISTS blockchain_listener.monitored_addresses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    address VARCHAR(255) NOT NULL,
    wallet_id UUID NOT NULL,
    user_id UUID NOT NULL,
    currency VARCHAR(20) NOT NULL,
    blockchain VARCHAR(20) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    last_checked_block BIGINT,
    last_activity_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(address, currency)
);

-- Table to track transaction processing status
CREATE TABLE IF NOT EXISTS blockchain_listener.transaction_processing (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    transaction_hash VARCHAR(255) NOT NULL UNIQUE,
    blockchain VARCHAR(20) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'detected', -- 'detected', 'processing', 'completed', 'failed'
    retry_count INTEGER DEFAULT 0,
    last_error TEXT,
    detected_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for webhook delivery tracking
CREATE TABLE IF NOT EXISTS blockchain_listener.webhook_deliveries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    transaction_hash VARCHAR(255) NOT NULL,
    webhook_url VARCHAR(500) NOT NULL,
    payload JSONB NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending', -- 'pending', 'delivered', 'failed'
    response_status INTEGER,
    response_body TEXT,
    retry_count INTEGER DEFAULT 0,
    next_retry_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_block_checkpoints_blockchain ON blockchain_listener.block_checkpoints(blockchain);
CREATE INDEX IF NOT EXISTS idx_monitored_addresses_address ON blockchain_listener.monitored_addresses(address);
CREATE INDEX IF NOT EXISTS idx_monitored_addresses_wallet_id ON blockchain_listener.monitored_addresses(wallet_id);
CREATE INDEX IF NOT EXISTS idx_monitored_addresses_user_id ON blockchain_listener.monitored_addresses(user_id);
CREATE INDEX IF NOT EXISTS idx_monitored_addresses_currency ON blockchain_listener.monitored_addresses(currency);
CREATE INDEX IF NOT EXISTS idx_monitored_addresses_blockchain ON blockchain_listener.monitored_addresses(blockchain);
CREATE INDEX IF NOT EXISTS idx_monitored_addresses_active ON blockchain_listener.monitored_addresses(is_active) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_transaction_processing_hash ON blockchain_listener.transaction_processing(transaction_hash);
CREATE INDEX IF NOT EXISTS idx_transaction_processing_blockchain ON blockchain_listener.transaction_processing(blockchain);
CREATE INDEX IF NOT EXISTS idx_transaction_processing_status ON blockchain_listener.transaction_processing(status);

CREATE INDEX IF NOT EXISTS idx_webhook_deliveries_hash ON blockchain_listener.webhook_deliveries(transaction_hash);
CREATE INDEX IF NOT EXISTS idx_webhook_deliveries_status ON blockchain_listener.webhook_deliveries(status);
CREATE INDEX IF NOT EXISTS idx_webhook_deliveries_retry ON blockchain_listener.webhook_deliveries(next_retry_at) WHERE status = 'failed';

-- Insert initial block checkpoints
INSERT INTO blockchain_listener.block_checkpoints (blockchain, last_processed_block) 
VALUES 
    ('bitcoin', 0),
    ('ethereum', 0),
    ('tron', 0)
ON CONFLICT (blockchain) DO NOTHING;

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION blockchain_listener.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers to automatically update updated_at
CREATE TRIGGER update_block_checkpoints_updated_at 
    BEFORE UPDATE ON blockchain_listener.block_checkpoints 
    FOR EACH ROW EXECUTE FUNCTION blockchain_listener.update_updated_at_column();

CREATE TRIGGER update_monitored_addresses_updated_at 
    BEFORE UPDATE ON blockchain_listener.monitored_addresses 
    FOR EACH ROW EXECUTE FUNCTION blockchain_listener.update_updated_at_column();

CREATE TRIGGER update_transaction_processing_updated_at 
    BEFORE UPDATE ON blockchain_listener.transaction_processing 
    FOR EACH ROW EXECUTE FUNCTION blockchain_listener.update_updated_at_column();

CREATE TRIGGER update_webhook_deliveries_updated_at 
    BEFORE UPDATE ON blockchain_listener.webhook_deliveries 
    FOR EACH ROW EXECUTE FUNCTION blockchain_listener.update_updated_at_column();
