use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Settlement {
    pub id: Uuid,
    pub user_id: Uuid,
    pub transaction_id: Option<Uuid>,
    pub crypto_amount: Decimal,
    pub crypto_currency: String,
    pub kes_amount: Decimal,
    pub exchange_rate: Decimal,
    pub recipient_phone: String,
    pub recipient_name: String,
    pub status: String, // "pending", "processing", "completed", "failed", "cancelled"
    pub yellowcard_transaction_id: Option<String>,
    pub mpesa_transaction_id: Option<String>,
    pub failure_reason: Option<String>,
    pub webhook_url: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub completed_at: Option<DateTime<Utc>>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, FromRow)]
pub struct ExchangeRate {
    pub id: Uuid,
    pub from_currency: String, // "BTC", "USDT"
    pub to_currency: String,   // "KES"
    pub rate: Decimal,
    pub source: String, // "yellowcard", "binance", "coingecko"
    pub created_at: DateTime<Utc>,
    pub expires_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct SettlementFee {
    pub id: Uuid,
    pub settlement_id: Uuid,
    pub fee_type: String, // "exchange", "network", "processing"
    pub amount: Decimal,
    pub currency: String,
    pub created_at: DateTime<Utc>,
}

// Request/Response DTOs
#[derive(Debug, Deserialize)]
pub struct CreateSettlementRequest {
    pub user_id: Uuid,
    pub transaction_id: Option<Uuid>,
    pub crypto_amount: String,
    pub crypto_currency: String, // "BTC", "USDT_ERC20", "USDT_TRC20"
    pub recipient_phone: String,
    pub recipient_name: String,
    pub webhook_url: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct SettlementResponse {
    pub id: Uuid,
    pub user_id: Uuid,
    pub transaction_id: Option<Uuid>,
    pub crypto_amount: String,
    pub crypto_currency: String,
    pub kes_amount: String,
    pub exchange_rate: String,
    pub recipient_phone: String,
    pub recipient_name: String,
    pub status: String,
    pub yellowcard_transaction_id: Option<String>,
    pub mpesa_transaction_id: Option<String>,
    pub failure_reason: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub completed_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Serialize)]
pub struct SettlementStatusResponse {
    pub id: Uuid,
    pub status: String,
    pub kes_amount: String,
    pub recipient_phone: String,
    pub yellowcard_transaction_id: Option<String>,
    pub mpesa_transaction_id: Option<String>,
    pub failure_reason: Option<String>,
    pub created_at: DateTime<Utc>,
    pub completed_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Serialize)]
pub struct ExchangeRateResponse {
    pub from_currency: String,
    pub to_currency: String,
    pub rate: String,
    pub source: String,
    pub timestamp: DateTime<Utc>,
    pub expires_at: DateTime<Utc>,
}

#[derive(Debug, Deserialize)]
pub struct GetExchangeRateRequest {
    pub from_currency: String,
    pub to_currency: String,
    pub amount: Option<String>,
}

// Webhook payloads
#[derive(Debug, Serialize, Deserialize)]
pub struct SettlementWebhookPayload {
    pub settlement_id: Uuid,
    pub status: String,
    pub crypto_amount: String,
    pub crypto_currency: String,
    pub kes_amount: String,
    pub recipient_phone: String,
    pub yellowcard_transaction_id: Option<String>,
    pub mpesa_transaction_id: Option<String>,
    pub completed_at: Option<DateTime<Utc>>,
    pub failure_reason: Option<String>,
}

// Internal processing models
#[derive(Debug, Clone)]
pub struct SettlementJob {
    pub settlement_id: Uuid,
    pub retry_count: u32,
    pub next_retry_at: DateTime<Utc>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CryptoToKesConversion {
    pub crypto_amount: Decimal,
    pub crypto_currency: String,
    pub kes_amount: Decimal,
    pub exchange_rate: Decimal,
    pub fees: Vec<ConversionFee>,
    pub total_fees: Decimal,
    pub net_kes_amount: Decimal,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConversionFee {
    pub fee_type: String,
    pub amount: Decimal,
    pub currency: String,
    pub description: String,
}

// Conversion from Settlement to SettlementResponse
impl From<Settlement> for SettlementResponse {
    fn from(settlement: Settlement) -> Self {
        SettlementResponse {
            id: settlement.id,
            user_id: settlement.user_id,
            transaction_id: settlement.transaction_id,
            crypto_amount: settlement.crypto_amount.to_string(),
            crypto_currency: settlement.crypto_currency,
            kes_amount: settlement.kes_amount.to_string(),
            exchange_rate: settlement.exchange_rate.to_string(),
            recipient_phone: settlement.recipient_phone,
            recipient_name: settlement.recipient_name,
            status: settlement.status,
            yellowcard_transaction_id: settlement.yellowcard_transaction_id,
            mpesa_transaction_id: settlement.mpesa_transaction_id,
            failure_reason: settlement.failure_reason,
            created_at: settlement.created_at,
            updated_at: settlement.updated_at,
            completed_at: settlement.completed_at,
        }
    }
}

// Conversion from Settlement to SettlementStatusResponse
impl From<Settlement> for SettlementStatusResponse {
    fn from(settlement: Settlement) -> Self {
        SettlementStatusResponse {
            id: settlement.id,
            status: settlement.status,
            kes_amount: settlement.kes_amount.to_string(),
            recipient_phone: settlement.recipient_phone,
            yellowcard_transaction_id: settlement.yellowcard_transaction_id,
            mpesa_transaction_id: settlement.mpesa_transaction_id,
            failure_reason: settlement.failure_reason,
            created_at: settlement.created_at,
            completed_at: settlement.completed_at,
        }
    }
}
