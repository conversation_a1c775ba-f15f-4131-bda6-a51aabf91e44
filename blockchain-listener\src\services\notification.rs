use reqwest::Client;
use serde_json::json;
use tracing::{info, error, warn};
use uuid::Uuid;

use crate::config::Config;
use crate::error::{AppError, Result};
use crate::models::{BlockchainEvent, MonitoredAddress, NotificationPayload};

pub struct NotificationService {
    http_client: Client,
    wallet_service_url: String,
    webhook_url: String,
}

impl NotificationService {
    pub fn new(config: &Config) -> Self {
        Self {
            http_client: Client::new(),
            wallet_service_url: config.wallet_service_url.clone(),
            webhook_url: config.notification_webhook_url.clone(),
        }
    }

    pub async fn send_transaction_notification(
        &self,
        event: &BlockchainEvent,
        monitored_address: &MonitoredAddress,
    ) -> Result<()> {
        // Create notification payload
        let payload = self.create_notification_payload(event, monitored_address)?;
        
        // Send to wallet service to update balance
        if let Err(e) = self.notify_wallet_service(&payload).await {
            error!("Failed to notify wallet service: {}", e);
        }
        
        // Send webhook notification
        if let Err(e) = self.send_webhook_notification(&payload).await {
            error!("Failed to send webhook notification: {}", e);
        }

        info!(
            "Sent transaction notification for {} {} to address {}",
            payload.amount, payload.currency, payload.to_address
        );

        Ok(())
    }

    fn create_notification_payload(
        &self,
        event: &BlockchainEvent,
        monitored_address: &MonitoredAddress,
    ) -> Result<NotificationPayload> {
        let (hash, amount, from_address, to_address, confirmations) = match event {
            BlockchainEvent::BitcoinTransaction(tx) => {
                let amount = tx.outputs.iter()
                    .find(|output| output.address.as_ref() == Some(&monitored_address.address))
                    .map(|output| output.value.to_string())
                    .unwrap_or_else(|| "0".to_string());
                
                (
                    tx.txid.clone(),
                    amount,
                    "unknown".to_string(), // Bitcoin inputs are complex
                    monitored_address.address.clone(),
                    tx.confirmations,
                )
            }
            BlockchainEvent::EthereumTransaction(tx) => {
                (
                    tx.hash.clone(),
                    tx.value.clone(),
                    tx.from.clone(),
                    tx.to.clone(),
                    tx.confirmations,
                )
            }
            BlockchainEvent::EthereumLog(log) => {
                // For USDT ERC20 transfers, decode the amount from log data
                let amount = self.decode_erc20_transfer_amount(&log.data)
                    .unwrap_or_else(|| "0".to_string());
                
                (
                    log.transaction_hash.clone(),
                    amount,
                    "decoded_from_log".to_string(), // Would decode from topics
                    monitored_address.address.clone(),
                    1, // Default confirmations for logs
                )
            }
            BlockchainEvent::TronTransaction(tx) => {
                (
                    tx.txid.clone(),
                    tx.amount.to_string(),
                    tx.from.clone(),
                    tx.to.clone(),
                    tx.confirmations,
                )
            }
        };

        Ok(NotificationPayload {
            transaction_id: Uuid::new_v4(),
            wallet_id: monitored_address.wallet_id,
            user_id: monitored_address.user_id,
            transaction_hash: hash,
            transaction_type: "incoming".to_string(),
            currency: event.get_currency(),
            amount,
            from_address,
            to_address,
            confirmations,
            status: if confirmations > 0 { "confirmed" } else { "pending" }.to_string(),
            timestamp: chrono::Utc::now(),
        })
    }

    fn decode_erc20_transfer_amount(&self, data: &str) -> Option<String> {
        // Simplified ERC20 transfer amount decoding
        // In production, properly decode the hex data
        if data.len() >= 66 { // 0x + 64 hex chars
            // The amount is the last 32 bytes (64 hex chars) of the data
            let amount_hex = &data[data.len() - 64..];
            if let Ok(amount) = u128::from_str_radix(amount_hex, 16) {
                return Some(amount.to_string());
            }
        }
        None
    }

    async fn notify_wallet_service(&self, payload: &NotificationPayload) -> Result<()> {
        let url = format!("{}/internal/transaction-notification", self.wallet_service_url);
        
        let response = self.http_client
            .post(&url)
            .json(payload)
            .send()
            .await
            .map_err(|e| AppError::Notification(format!("Failed to notify wallet service: {}", e)))?;

        if !response.status().is_success() {
            let status = response.status();
            let body = response.text().await.unwrap_or_else(|_| "Unknown error".to_string());
            return Err(AppError::Notification(format!(
                "Wallet service returned error {}: {}",
                status, body
            )));
        }

        info!("Successfully notified wallet service about transaction {}", payload.transaction_hash);
        Ok(())
    }

    async fn send_webhook_notification(&self, payload: &NotificationPayload) -> Result<()> {
        let webhook_payload = json!({
            "event": "transaction_received",
            "data": payload
        });

        let response = self.http_client
            .post(&self.webhook_url)
            .json(&webhook_payload)
            .send()
            .await
            .map_err(|e| AppError::Notification(format!("Failed to send webhook: {}", e)))?;

        if !response.status().is_success() {
            let status = response.status();
            let body = response.text().await.unwrap_or_else(|_| "Unknown error".to_string());
            warn!("Webhook returned error {}: {}", status, body);
            // Don't fail the entire notification for webhook errors
        } else {
            info!("Successfully sent webhook notification for transaction {}", payload.transaction_hash);
        }

        Ok(())
    }

    pub async fn send_balance_update_notification(
        &self,
        wallet_id: Uuid,
        user_id: Uuid,
        currency: &str,
        old_balance: &str,
        new_balance: &str,
    ) -> Result<()> {
        let payload = json!({
            "event": "balance_updated",
            "data": {
                "wallet_id": wallet_id,
                "user_id": user_id,
                "currency": currency,
                "old_balance": old_balance,
                "new_balance": new_balance,
                "timestamp": chrono::Utc::now()
            }
        });

        let response = self.http_client
            .post(&self.webhook_url)
            .json(&payload)
            .send()
            .await
            .map_err(|e| AppError::Notification(format!("Failed to send balance update webhook: {}", e)))?;

        if !response.status().is_success() {
            let status = response.status();
            let body = response.text().await.unwrap_or_else(|_| "Unknown error".to_string());
            warn!("Balance update webhook returned error {}: {}", status, body);
        } else {
            info!("Successfully sent balance update notification for wallet {}", wallet_id);
        }

        Ok(())
    }

    pub async fn send_confirmation_update(
        &self,
        transaction_hash: &str,
        confirmations: u32,
        is_final: bool,
    ) -> Result<()> {
        let payload = json!({
            "event": "confirmation_update",
            "data": {
                "transaction_hash": transaction_hash,
                "confirmations": confirmations,
                "is_final": is_final,
                "timestamp": chrono::Utc::now()
            }
        });

        let response = self.http_client
            .post(&self.webhook_url)
            .json(&payload)
            .send()
            .await
            .map_err(|e| AppError::Notification(format!("Failed to send confirmation update webhook: {}", e)))?;

        if !response.status().is_success() {
            let status = response.status();
            let body = response.text().await.unwrap_or_else(|_| "Unknown error".to_string());
            warn!("Confirmation update webhook returned error {}: {}", status, body);
        } else {
            info!("Successfully sent confirmation update for transaction {}", transaction_hash);
        }

        Ok(())
    }
}
