# Qpesapay Blockchain Integration Tests
Write-Host "🧪 Starting Blockchain Monitoring Integration Tests..." -ForegroundColor Green

# Test 1: Create Bitcoin Wallet
Write-Host "`n📝 Test 1: Creating Bitcoin Wallet..." -ForegroundColor Yellow
$headers = @{'Content-Type' = 'application/json'}
$body = '{"user_id": "test-user-123", "currency": "BTC"}'

try {
    $response = Invoke-WebRequest -Uri 'http://localhost:5003/wallets' -Method POST -Headers $headers -Body $body -UseBasicParsing
    $walletData = $response.Content | ConvertFrom-Json
    Write-Host "✅ Bitcoin wallet created successfully!" -ForegroundColor Green
    Write-Host "   Wallet ID: $($walletData.wallet_id)" -ForegroundColor Cyan
    Write-Host "   Address: $($walletData.address)" -ForegroundColor Cyan
    $btcAddress = $walletData.address
    $btcWalletId = $walletData.wallet_id
} catch {
    Write-Host "❌ Failed to create Bitcoin wallet: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test 2: Create Ethereum USDT Wallet
Write-Host "`n📝 Test 2: Creating Ethereum USDT Wallet..." -ForegroundColor Yellow
$body = '{"user_id": "test-user-123", "currency": "USDT_ERC20"}'

try {
    $response = Invoke-WebRequest -Uri 'http://localhost:5003/wallets' -Method POST -Headers $headers -Body $body -UseBasicParsing
    $walletData = $response.Content | ConvertFrom-Json
    Write-Host "✅ Ethereum USDT wallet created successfully!" -ForegroundColor Green
    Write-Host "   Wallet ID: $($walletData.wallet_id)" -ForegroundColor Cyan
    Write-Host "   Address: $($walletData.address)" -ForegroundColor Cyan
    $ethAddress = $walletData.address
    $ethWalletId = $walletData.wallet_id
} catch {
    Write-Host "❌ Failed to create Ethereum USDT wallet: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test 3: Create TRON USDT Wallet
Write-Host "`n📝 Test 3: Creating TRON USDT Wallet..." -ForegroundColor Yellow
$body = '{"user_id": "test-user-123", "currency": "USDT_TRC20"}'

try {
    $response = Invoke-WebRequest -Uri 'http://localhost:5003/wallets' -Method POST -Headers $headers -Body $body -UseBasicParsing
    $walletData = $response.Content | ConvertFrom-Json
    Write-Host "✅ TRON USDT wallet created successfully!" -ForegroundColor Green
    Write-Host "   Wallet ID: $($walletData.wallet_id)" -ForegroundColor Cyan
    Write-Host "   Address: $($walletData.address)" -ForegroundColor Cyan
    $tronAddress = $walletData.address
    $tronWalletId = $walletData.wallet_id
} catch {
    Write-Host "❌ Failed to create TRON USDT wallet: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test 4: Check Blockchain Listener Health
Write-Host "`n📝 Test 4: Checking Blockchain Listener Health..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri 'http://localhost:5004/health' -UseBasicParsing
    $healthData = $response.Content | ConvertFrom-Json
    Write-Host "✅ Blockchain Listener is healthy!" -ForegroundColor Green
    Write-Host "   Status: $($healthData.status)" -ForegroundColor Cyan
    Write-Host "   Database: $($healthData.database)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ Blockchain Listener health check failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test 5: Check Blockchain Listener Logs
Write-Host "`n📝 Test 5: Checking Blockchain Listener Activity..." -ForegroundColor Yellow
try {
    $logs = docker logs qpesapay-blockchain-listener --tail 10 2>&1
    $recentLogs = $logs | Select-Object -Last 5
    Write-Host "✅ Blockchain Listener is actively monitoring:" -ForegroundColor Green
    foreach ($log in $recentLogs) {
        if ($log -match "Processing.*blocks") {
            Write-Host "   $log" -ForegroundColor Cyan
        }
    }
} catch {
    Write-Host "❌ Failed to check blockchain listener logs: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🎉 Integration Tests Completed!" -ForegroundColor Green
Write-Host "📊 Summary:" -ForegroundColor Yellow
Write-Host "   - Bitcoin Address: $btcAddress" -ForegroundColor White
Write-Host "   - Ethereum Address: $ethAddress" -ForegroundColor White  
Write-Host "   - TRON Address: $tronAddress" -ForegroundColor White
Write-Host "   - All services are healthy and communicating" -ForegroundColor White
Write-Host "   - Blockchain listeners are actively monitoring for transactions" -ForegroundColor White
