use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, FromRow)]
pub struct WalletAddress {
    pub id: Uuid,
    pub wallet_id: Uuid,
    pub address: String,
    pub derivation_index: i32,
    pub is_used: bool,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, FromRow)]
pub struct Transaction {
    pub id: Uuid,
    pub user_id: Option<Uuid>,
    pub wallet_id: Option<Uuid>,
    pub transaction_hash: Option<String>,
    pub transaction_type: String,
    pub currency: String,
    pub amount: rust_decimal::Decimal,
    pub fee: rust_decimal::Decimal,
    pub from_address: Option<String>,
    pub to_address: Option<String>,
    pub status: String,
    pub confirmations: i32,
    pub block_number: Option<i64>,
    pub network: Option<String>,
    pub created_at: DateTime<Utc>,
    pub confirmed_at: Option<DateTime<Utc>>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct BlockchainTransaction {
    pub hash: String,
    pub from_address: String,
    pub to_address: String,
    pub amount: String,
    pub fee: String,
    pub block_number: u64,
    pub confirmations: u32,
    pub currency: String,
    pub network: String,
    pub timestamp: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BitcoinTransaction {
    pub txid: String,
    pub inputs: Vec<BitcoinInput>,
    pub outputs: Vec<BitcoinOutput>,
    pub fee: u64,
    pub block_height: Option<u64>,
    pub confirmations: u32,
    pub timestamp: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BitcoinInput {
    pub txid: String,
    pub vout: u32,
    pub value: u64,
    pub address: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BitcoinOutput {
    pub value: u64,
    pub address: Option<String>,
    pub script_pubkey: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EthereumTransaction {
    pub hash: String,
    pub from: String,
    pub to: String,
    pub value: String,
    pub gas: String,
    pub gas_price: String,
    pub gas_used: Option<String>,
    pub nonce: u64,
    pub block_number: Option<u64>,
    pub block_hash: Option<String>,
    pub transaction_index: Option<u64>,
    pub confirmations: u32,
    pub timestamp: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EthereumLog {
    pub address: String,
    pub topics: Vec<String>,
    pub data: String,
    pub block_number: Option<u64>,
    pub transaction_hash: String,
    pub transaction_index: Option<u64>,
    pub log_index: Option<u64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TronTransaction {
    pub txid: String,
    pub from: String,
    pub to: String,
    pub amount: u64,
    pub fee: u64,
    pub block_number: u64,
    pub block_timestamp: u64,
    pub confirmations: u32,
    pub contract_address: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NotificationPayload {
    pub transaction_id: Uuid,
    pub wallet_id: Uuid,
    pub user_id: Uuid,
    pub transaction_hash: String,
    pub transaction_type: String,
    pub currency: String,
    pub amount: String,
    pub from_address: String,
    pub to_address: String,
    pub confirmations: u32,
    pub status: String,
    pub timestamp: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitoredAddress {
    pub address: String,
    pub wallet_id: Uuid,
    pub user_id: Uuid,
    pub currency: String,
    pub last_checked_block: Option<u64>,
}

// Blockchain-specific event types
#[derive(Debug, Clone)]
pub enum BlockchainEvent {
    BitcoinTransaction(BitcoinTransaction),
    EthereumTransaction(EthereumTransaction),
    EthereumLog(EthereumLog),
    TronTransaction(TronTransaction),
}

impl BlockchainEvent {
    pub fn get_hash(&self) -> String {
        match self {
            BlockchainEvent::BitcoinTransaction(tx) => tx.txid.clone(),
            BlockchainEvent::EthereumTransaction(tx) => tx.hash.clone(),
            BlockchainEvent::EthereumLog(log) => log.transaction_hash.clone(),
            BlockchainEvent::TronTransaction(tx) => tx.txid.clone(),
        }
    }

    pub fn get_currency(&self) -> String {
        match self {
            BlockchainEvent::BitcoinTransaction(_) => "BTC".to_string(),
            BlockchainEvent::EthereumTransaction(_) => "ETH".to_string(),
            BlockchainEvent::EthereumLog(_) => "USDT_ERC20".to_string(),
            BlockchainEvent::TronTransaction(_) => "USDT_TRC20".to_string(),
        }
    }

    pub fn get_block_number(&self) -> Option<u64> {
        match self {
            BlockchainEvent::BitcoinTransaction(tx) => tx.block_height,
            BlockchainEvent::EthereumTransaction(tx) => tx.block_number,
            BlockchainEvent::EthereumLog(log) => log.block_number,
            BlockchainEvent::TronTransaction(tx) => Some(tx.block_number),
        }
    }
}
