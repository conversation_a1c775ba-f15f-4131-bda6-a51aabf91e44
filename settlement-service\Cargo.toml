[package]
name = "qpesapay-settlement-service"
version = "0.1.0"
edition = "2021"

[dependencies]
# Async runtime
tokio = { version = "1.0", features = ["full"] }
futures = "0.3"

# Web framework
axum = "0.7"
tower = "0.4"
tower-http = { version = "0.5", features = ["cors", "trace"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Database
sqlx = { version = "0.8", features = ["runtime-tokio-rustls", "postgres", "uuid", "chrono", "json"] }
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }

# HTTP client
reqwest = { version = "0.12", features = ["json"] }

# Logging and tracing
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# Configuration
config = "0.14"
dotenv = "0.15"

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Async traits
async-trait = "0.1"

# Decimal arithmetic
rust_decimal = { version = "1.0", features = ["serde"] }

# Environment variables
envy = "0.4"

# Retry logic
backoff = { version = "0.4", features = ["tokio"] }

# Cryptography for API signatures
hmac = "0.12"
sha2 = "0.10"
hex = "0.4"

# Base64 encoding
base64 = "0.22"

# Phone number validation
phonenumber = "0.3"

# Queue processing
tokio-cron-scheduler = "0.13"

# Metrics
prometheus = "0.13"
