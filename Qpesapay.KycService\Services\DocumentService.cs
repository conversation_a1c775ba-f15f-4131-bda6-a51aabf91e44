using Microsoft.EntityFrameworkCore;
using Qpesapay.KycService.Data;
using Qpesapay.KycService.Models;

namespace Qpesapay.KycService.Services;

public interface IDocumentService
{
    Task<KycDocument> UploadDocumentAsync(Guid profileId, string documentType, IFormFile file);
    Task<KycDocument?> GetDocumentAsync(Guid documentId);
    Task<List<KycDocument>> GetDocumentsByProfileAsync(Guid profileId);
    Task<bool> UpdateDocumentVerificationAsync(Guid documentId, string status, string? notes = null);
    Task<bool> DeleteDocumentAsync(Guid documentId);
    Task<string> SaveFileAsync(IFormFile file, string documentType, Guid profileId);
    Task<bool> DeleteFileAsync(string filePath);
}

public class DocumentService : IDocumentService
{
    private readonly KycDbContext _context;
    private readonly IConfiguration _configuration;
    private readonly ILogger<DocumentService> _logger;
    private readonly string _uploadPath;
    private readonly long _maxFileSize;
    private readonly string[] _allowedExtensions;

    public DocumentService(
        KycDbContext context, 
        IConfiguration configuration, 
        ILogger<DocumentService> logger)
    {
        _context = context;
        _configuration = configuration;
        _logger = logger;
        
        _uploadPath = _configuration["FileStorage:BasePath"] ?? "/app/uploads";
        _maxFileSize = long.Parse(_configuration["FileStorage:MaxFileSize"] ?? "10485760"); // 10MB default
        _allowedExtensions = _configuration.GetSection("FileStorage:AllowedExtensions")
            .Get<string[]>() ?? new[] { ".jpg", ".jpeg", ".png", ".pdf" };
    }

    public async Task<KycDocument> UploadDocumentAsync(Guid profileId, string documentType, IFormFile file)
    {
        try
        {
            // Validate profile exists
            var profile = await _context.Profiles.FindAsync(profileId);
            if (profile == null)
            {
                throw new ArgumentException("Profile not found");
            }

            // Validate document type
            var validTypes = new[] { "id_front", "id_back", "selfie", "proof_of_address" };
            if (!validTypes.Contains(documentType.ToLower()))
            {
                throw new ArgumentException("Invalid document type");
            }

            // Validate file
            ValidateFile(file);

            // Check if document of this type already exists
            var existingDoc = await _context.Documents
                .FirstOrDefaultAsync(d => d.ProfileId == profileId && d.DocumentType == documentType.ToLower());
            
            if (existingDoc != null)
            {
                // Delete old file and document record
                await DeleteFileAsync(existingDoc.FilePath);
                _context.Documents.Remove(existingDoc);
            }

            // Save file
            var filePath = await SaveFileAsync(file, documentType, profileId);

            // Create document record
            var document = new KycDocument
            {
                ProfileId = profileId,
                DocumentType = documentType.ToLower(),
                FilePath = filePath,
                FileSize = (int)file.Length,
                MimeType = file.ContentType,
                VerificationStatus = "pending",
                UploadedAt = DateTime.UtcNow
            };

            _context.Documents.Add(document);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Document uploaded: {DocumentType} for profile {ProfileId}", 
                documentType, profileId);
            
            return document;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading document: {DocumentType} for profile {ProfileId}", 
                documentType, profileId);
            throw;
        }
    }

    public async Task<KycDocument?> GetDocumentAsync(Guid documentId)
    {
        try
        {
            return await _context.Documents
                .Include(d => d.Profile)
                .FirstOrDefaultAsync(d => d.Id == documentId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving document: {DocumentId}", documentId);
            throw;
        }
    }

    public async Task<List<KycDocument>> GetDocumentsByProfileAsync(Guid profileId)
    {
        try
        {
            return await _context.Documents
                .Where(d => d.ProfileId == profileId)
                .OrderBy(d => d.UploadedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving documents for profile: {ProfileId}", profileId);
            throw;
        }
    }

    public async Task<bool> UpdateDocumentVerificationAsync(Guid documentId, string status, string? notes = null)
    {
        try
        {
            var document = await _context.Documents.FindAsync(documentId);
            if (document == null)
            {
                return false;
            }

            // Validate status
            var validStatuses = new[] { "pending", "verified", "rejected" };
            if (!validStatuses.Contains(status.ToLower()))
            {
                throw new ArgumentException("Invalid verification status");
            }

            document.VerificationStatus = status.ToLower();
            document.VerificationNotes = notes;
            
            if (status.ToLower() == "verified")
            {
                document.VerifiedAt = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();

            _logger.LogInformation("Document verification updated: {DocumentId}, Status: {Status}", 
                documentId, status);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating document verification: {DocumentId}", documentId);
            throw;
        }
    }

    public async Task<bool> DeleteDocumentAsync(Guid documentId)
    {
        try
        {
            var document = await _context.Documents.FindAsync(documentId);
            if (document == null)
            {
                return false;
            }

            // Delete file from storage
            await DeleteFileAsync(document.FilePath);

            // Delete database record
            _context.Documents.Remove(document);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Document deleted: {DocumentId}", documentId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting document: {DocumentId}", documentId);
            throw;
        }
    }

    public async Task<string> SaveFileAsync(IFormFile file, string documentType, Guid profileId)
    {
        try
        {
            // Create directory if it doesn't exist
            var profileDir = Path.Combine(_uploadPath, profileId.ToString());
            Directory.CreateDirectory(profileDir);

            // Generate unique filename
            var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
            var fileName = $"{documentType}_{DateTime.UtcNow:yyyyMMdd_HHmmss}{extension}";
            var filePath = Path.Combine(profileDir, fileName);

            // Save file
            using var stream = new FileStream(filePath, FileMode.Create);
            await file.CopyToAsync(stream);

            _logger.LogInformation("File saved: {FilePath}", filePath);
            return filePath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving file for profile: {ProfileId}", profileId);
            throw;
        }
    }

    public async Task<bool> DeleteFileAsync(string filePath)
    {
        try
        {
            if (File.Exists(filePath))
            {
                File.Delete(filePath);
                _logger.LogInformation("File deleted: {FilePath}", filePath);
            }
            return await Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting file: {FilePath}", filePath);
            return false;
        }
    }

    private void ValidateFile(IFormFile file)
    {
        if (file == null || file.Length == 0)
        {
            throw new ArgumentException("File is required");
        }

        if (file.Length > _maxFileSize)
        {
            throw new ArgumentException($"File size exceeds maximum allowed size of {_maxFileSize / 1024 / 1024}MB");
        }

        var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
        if (!_allowedExtensions.Contains(extension))
        {
            throw new ArgumentException($"File type not allowed. Allowed types: {string.Join(", ", _allowedExtensions)}");
        }

        // Additional MIME type validation
        var allowedMimeTypes = new[]
        {
            "image/jpeg", "image/jpg", "image/png", "application/pdf"
        };
        
        if (!allowedMimeTypes.Contains(file.ContentType.ToLowerInvariant()))
        {
            throw new ArgumentException("Invalid file type");
        }
    }
}
