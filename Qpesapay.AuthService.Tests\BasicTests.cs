using Xunit;
using FluentAssertions;
using Qpesapay.AuthService.Models;

namespace Qpesapay.AuthService.Tests;

public class BasicTests
{
    [Fact]
    public void SignupRequest_ShouldCreateWithValidData()
    {
        // Arrange & Act
        var request = new SignupRequest(
            "<EMAIL>",
            "SecurePassword123!",
            "+************",
            "personal"
        );

        // Assert
        request.Email.Should().Be("<EMAIL>");
        request.Password.Should().Be("SecurePassword123!");
        request.PhoneNumber.Should().Be("+************");
        request.Role.Should().Be("personal");
    }

    [Fact]
    public void LoginRequest_ShouldCreateWithValidData()
    {
        // Arrange & Act
        var request = new LoginRequest("<EMAIL>", "password123");

        // Assert
        request.Email.Should().Be("<EMAIL>");
        request.Password.Should().Be("password123");
    }

    [Fact]
    public void OtpRequest_ShouldCreateWithValidData()
    {
        // Arrange & Act
        var request = new OtpRequest("<EMAIL>", "123456");

        // Assert
        request.Email.Should().Be("<EMAIL>");
        request.OtpCode.Should().Be("123456");
    }

    [Fact]
    public void UserResponse_ShouldCreateWithValidData()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var createdAt = DateTime.UtcNow;

        // Act
        var response = new UserResponse(
            userId,
            "<EMAIL>",
            "+************",
            "personal",
            true,
            true,
            createdAt
        );

        // Assert
        response.Id.Should().Be(userId);
        response.Email.Should().Be("<EMAIL>");
        response.PhoneNumber.Should().Be("+************");
        response.Role.Should().Be("personal");
        response.IsVerified.Should().BeTrue();
        response.IsActive.Should().BeTrue();
        response.CreatedAt.Should().Be(createdAt);
    }

    [Fact]
    public void LoginResponse_ShouldCreateWithValidData()
    {
        // Arrange
        var userResponse = new UserResponse(
            Guid.NewGuid(),
            "<EMAIL>",
            "+************",
            "personal",
            true,
            true,
            DateTime.UtcNow
        );

        // Act
        var response = new LoginResponse("jwt-token-here", userResponse);

        // Assert
        response.Token.Should().Be("jwt-token-here");
        response.User.Should().Be(userResponse);
    }

    [Fact]
    public void User_ShouldInitializeWithDefaults()
    {
        // Act
        var user = new User();

        // Assert
        user.Id.Should().NotBe(Guid.Empty);
        user.Email.Should().Be(string.Empty);
        user.PasswordHash.Should().Be(string.Empty);
        user.Role.Should().Be("personal");
        user.IsVerified.Should().BeFalse();
        user.IsActive.Should().BeTrue();
        user.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
        user.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
    }

    [Fact]
    public void OtpCode_ShouldInitializeWithDefaults()
    {
        // Act
        var otpCode = new OtpCode();

        // Assert
        otpCode.Id.Should().NotBe(Guid.Empty);
        otpCode.Email.Should().Be(string.Empty);
        otpCode.Code.Should().Be(string.Empty);
        otpCode.Purpose.Should().Be(string.Empty);
        otpCode.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
        otpCode.UsedAt.Should().BeNull();
    }

    [Fact]
    public void UserSession_ShouldInitializeWithDefaults()
    {
        // Act
        var session = new UserSession();

        // Assert
        session.Id.Should().NotBe(Guid.Empty);
        session.TokenHash.Should().Be(string.Empty);
        session.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
        session.LastUsedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
    }

    [Theory]
    [InlineData("personal")]
    [InlineData("merchant")]
    public void SignupRequest_ShouldAcceptValidRoles(string role)
    {
        // Act
        var request = new SignupRequest("<EMAIL>", "password", "+************", role);

        // Assert
        request.Role.Should().Be(role);
    }

    [Fact]
    public void SignupRequest_ShouldDefaultToPersonalRole()
    {
        // Act
        var request = new SignupRequest("<EMAIL>", "password", "+************");

        // Assert
        request.Role.Should().Be("personal");
    }
}
