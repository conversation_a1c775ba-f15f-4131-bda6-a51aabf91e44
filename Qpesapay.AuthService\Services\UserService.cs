using Microsoft.EntityFrameworkCore;
using Qpesapay.AuthService.Data;
using Qpesapay.AuthService.Models;
using BCrypt.Net;

namespace Qpesapay.AuthService.Services;

public interface IUserService
{
    Task<User?> GetUserByEmailAsync(string email);
    Task<User?> GetUserByIdAsync(Guid id);
    Task<User> CreateUserAsync(SignupRequest request);
    Task<bool> ValidatePasswordAsync(string email, string password);
    Task<bool> UpdateUserVerificationAsync(string email, bool isVerified);
    Task<UserResponse> MapToUserResponseAsync(User user);
}

public class UserService : IUserService
{
    private readonly AuthDbContext _context;
    private readonly ILogger<UserService> _logger;

    public UserService(AuthDbContext context, ILogger<UserService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<User?> GetUserByEmailAsync(string email)
    {
        try
        {
            return await _context.Users
                .FirstOrDefaultAsync(u => u.Email.ToLower() == email.ToLower());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving user by email: {Email}", email);
            throw;
        }
    }

    public async Task<User?> GetUserByIdAsync(Guid id)
    {
        try
        {
            return await _context.Users
                .FirstOrDefaultAsync(u => u.Id == id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving user by ID: {UserId}", id);
            throw;
        }
    }

    public async Task<User> CreateUserAsync(SignupRequest request)
    {
        try
        {
            // Check if user already exists
            var existingUser = await GetUserByEmailAsync(request.Email);
            if (existingUser != null)
            {
                throw new InvalidOperationException("User with this email already exists");
            }

            // Validate role
            if (!IsValidRole(request.Role))
            {
                throw new ArgumentException("Invalid role specified");
            }

            // Hash password
            var passwordHash = BCrypt.Net.BCrypt.HashPassword(request.Password, BCrypt.Net.BCrypt.GenerateSalt(12));

            var user = new User
            {
                Email = request.Email.ToLower().Trim(),
                PasswordHash = passwordHash,
                PhoneNumber = request.PhoneNumber?.Trim(),
                Role = request.Role,
                IsVerified = false,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _context.Users.Add(user);
            await _context.SaveChangesAsync();

            _logger.LogInformation("User created successfully: {Email}", user.Email);
            return user;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating user: {Email}", request.Email);
            throw;
        }
    }

    public async Task<bool> ValidatePasswordAsync(string email, string password)
    {
        try
        {
            var user = await GetUserByEmailAsync(email);
            if (user == null || !user.IsActive)
            {
                return false;
            }

            return BCrypt.Net.BCrypt.Verify(password, user.PasswordHash);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating password for user: {Email}", email);
            return false;
        }
    }

    public async Task<bool> UpdateUserVerificationAsync(string email, bool isVerified)
    {
        try
        {
            var user = await GetUserByEmailAsync(email);
            if (user == null)
            {
                return false;
            }

            user.IsVerified = isVerified;
            user.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("User verification updated: {Email}, Verified: {IsVerified}", email, isVerified);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating user verification: {Email}", email);
            throw;
        }
    }

    public async Task<UserResponse> MapToUserResponseAsync(User user)
    {
        return await Task.FromResult(new UserResponse(
            user.Id,
            user.Email,
            user.PhoneNumber,
            user.Role,
            user.IsVerified,
            user.IsActive,
            user.CreatedAt
        ));
    }

    private static bool IsValidRole(string role)
    {
        return role is "personal" or "merchant";
    }
}

// Extension methods for User entity
public static class UserExtensions
{
    public static UserResponse ToUserResponse(this User user)
    {
        return new UserResponse(
            user.Id,
            user.Email,
            user.PhoneNumber,
            user.Role,
            user.IsVerified,
            user.IsActive,
            user.CreatedAt
        );
    }
}
