use serde::Deserialize;
use std::env;
use crate::error::{AppError, Result};

#[derive(Debug, Clone, Deserialize)]
pub struct Config {
    pub database_url: String,
    pub port: u16,
    pub encryption_key: String,
    pub bitcoin_network: String,
    pub ethereum_rpc_url: String,
    pub tron_rpc_url: String,
    pub jwt_secret: String,
}

impl Config {
    pub fn from_env() -> Result<Self> {
        dotenv::dotenv().ok();

        let config = Config {
            database_url: env::var("DATABASE_URL")
                .unwrap_or_else(|_| "postgresql://qpesapay_user:qpesapay_password_change_in_production@localhost:5432/qpesapay_db".to_string()),
            port: env::var("PORT")
                .unwrap_or_else(|_| "8080".to_string())
                .parse()
                .map_err(|_| AppError::ConfigError("Invalid port number".to_string()))?,
            encryption_key: env::var("ENCRYPTION_KEY")
                .map_err(|_| AppError::ConfigError("ENCRYPTION_KEY environment variable is required".to_string()))?,
            bitcoin_network: env::var("BITCOIN_NETWORK")
                .unwrap_or_else(|_| "testnet".to_string()),
            ethereum_rpc_url: env::var("ETHEREUM_RPC_URL")
                .unwrap_or_else(|_| "https://sepolia.infura.io/v3/YOUR_PROJECT_ID".to_string()),
            tron_rpc_url: env::var("TRON_RPC_URL")
                .unwrap_or_else(|_| "https://api.shasta.trongrid.io".to_string()),
            jwt_secret: env::var("JWT_SECRET")
                .map_err(|_| AppError::ConfigError("JWT_SECRET environment variable is required".to_string()))?,
        };

        Ok(config)
    }
}
