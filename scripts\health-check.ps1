#!/usr/bin/env pwsh

<#
.SYNOPSIS
    Health check script for Qpesapay services
.DESCRIPTION
    Validates that all Qpesapay microservices are running and responding correctly
#>

# Colors for output
$Green = "`e[32m"
$Yellow = "`e[33m"
$Red = "`e[31m"
$Blue = "`e[34m"
$Reset = "`e[0m"

function Write-ColorOutput {
    param([string]$Message, [string]$Color = $Reset)
    Write-Host "$Color$Message$Reset"
}

function Test-ServiceHealth {
    param(
        [string]$ServiceName,
        [string]$Url,
        [int]$TimeoutSec = 10
    )
    
    try {
        $response = Invoke-WebRequest -Uri $Url -TimeoutSec $TimeoutSec -ErrorAction Stop
        if ($response.StatusCode -eq 200) {
            Write-ColorOutput "✅ $ServiceName - Healthy" $Green
            return $true
        } else {
            Write-ColorOutput "⚠️  $ServiceName - Unexpected status: $($response.StatusCode)" $Yellow
            return $false
        }
    }
    catch {
        Write-ColorOutput "❌ $ServiceName - Not responding: $($_.Exception.Message)" $Red
        return $false
    }
}

function Test-DatabaseConnection {
    try {
        $result = docker exec qpesapay-postgres pg_isready -U qpesapay_user -d qpesapay_db
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✅ PostgreSQL - Connected" $Green
            return $true
        } else {
            Write-ColorOutput "❌ PostgreSQL - Connection failed" $Red
            return $false
        }
    }
    catch {
        Write-ColorOutput "❌ PostgreSQL - Container not accessible" $Red
        return $false
    }
}

function Test-RedisConnection {
    try {
        $result = docker exec qpesapay-redis redis-cli ping
        if ($result -eq "PONG") {
            Write-ColorOutput "✅ Redis - Connected" $Green
            return $true
        } else {
            Write-ColorOutput "❌ Redis - Connection failed" $Red
            return $false
        }
    }
    catch {
        Write-ColorOutput "❌ Redis - Container not accessible" $Red
        return $false
    }
}

function Get-ContainerStatus {
    Write-ColorOutput "`n📊 Container Status:" $Blue
    docker-compose ps --format table
}

function Get-ServiceLogs {
    param([string]$ServiceName, [int]$Lines = 10)
    
    Write-ColorOutput "`n📋 Recent logs for $ServiceName (last $Lines lines):" $Blue
    docker-compose logs --tail=$Lines $ServiceName
}

# Main health check
Write-ColorOutput "🏥 Qpesapay System Health Check" $Blue
Write-ColorOutput "===============================" $Blue

$healthyServices = 0
$totalServices = 0

# Test infrastructure services
Write-ColorOutput "`n🗄️  Infrastructure Services:" $Blue
$totalServices += 2
if (Test-DatabaseConnection) { $healthyServices++ }
if (Test-RedisConnection) { $healthyServices++ }

# Test application services
Write-ColorOutput "`n🚀 Application Services:" $Blue
$services = @{
    "Auth Service" = "http://localhost:5001/health"
    "KYC Service" = "http://localhost:5002/health"
    "Wallet Service" = "http://localhost:8001/health"
    "Blockchain Listener" = "http://localhost:8002/health"
    "Settlement Service" = "http://localhost:8003/health"
}

foreach ($service in $services.Keys) {
    $totalServices++
    if (Test-ServiceHealth -ServiceName $service -Url $services[$service]) {
        $healthyServices++
    }
}

# Summary
Write-ColorOutput "`n📈 Health Summary:" $Blue
$healthPercentage = [math]::Round(($healthyServices / $totalServices) * 100, 1)

if ($healthyServices -eq $totalServices) {
    Write-ColorOutput "🎉 All services healthy! ($healthyServices/$totalServices - $healthPercentage%)" $Green
} elseif ($healthyServices -gt ($totalServices * 0.7)) {
    Write-ColorOutput "⚠️  Most services healthy ($healthyServices/$totalServices - $healthPercentage%)" $Yellow
} else {
    Write-ColorOutput "❌ System unhealthy ($healthyServices/$totalServices - $healthPercentage%)" $Red
}

# Show container status
Get-ContainerStatus

# Show logs for failed services if any
if ($healthyServices -lt $totalServices) {
    Write-ColorOutput "`n🔍 Checking logs for potentially failed services..." $Yellow
    
    # Check which containers are not running
    $containers = docker-compose ps --format json | ConvertFrom-Json
    foreach ($container in $containers) {
        if ($container.State -ne "running") {
            Get-ServiceLogs -ServiceName $container.Service -Lines 20
        }
    }
}

Write-ColorOutput "`n💡 Use 'docker-compose logs -f [service-name]' for real-time logs" $Yellow
Write-ColorOutput "💡 Use 'docker-compose restart [service-name]' to restart a service" $Yellow
