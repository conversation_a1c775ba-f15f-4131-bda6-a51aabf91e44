pub mod bitcoin;
pub mod ethereum;
pub mod tron;

pub use bitcoin::BitcoinListener;
pub use ethereum::EthereumListener;
pub use tron::TronListener;

use async_trait::async_trait;
use crate::error::Result;

#[async_trait]
pub trait BlockchainListener {
    async fn start(&self) -> Result<()>;
    async fn stop(&self) -> Result<()>;
    async fn get_status(&self) -> ListenerStatus;
}

#[derive(Debug, Clone)]
pub struct ListenerStatus {
    pub is_running: bool,
    pub last_block_processed: Option<u64>,
    pub last_error: Option<String>,
    pub processed_transactions: u64,
}
