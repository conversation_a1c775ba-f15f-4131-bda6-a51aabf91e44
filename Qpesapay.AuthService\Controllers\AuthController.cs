using Microsoft.AspNetCore.Mvc;
using Qpesapay.AuthService.Models;
using Qpesapay.AuthService.Services;

namespace Qpesapay.AuthService.Controllers;

[ApiController]
[Route("api/[controller]")]
public class AuthController : ControllerBase
{
    private readonly IJwtService _jwtService;
    private readonly IOtpService _otpService;
    private readonly IUserService _userService;
    private readonly ILogger<AuthController> _logger;

    public AuthController(
        IJwtService jwtService,
        IOtpService otpService,
        IUserService userService,
        ILogger<AuthController> logger)
    {
        _jwtService = jwtService;
        _otpService = otpService;
        _userService = userService;
        _logger = logger;
    }

    [HttpPost("signup")]
    public async Task<IActionResult> Signup([FromBody] SignupRequest request)
    {
        try
        {
            if (string.IsNullOrEmpty(request.Email) || string.IsNullOrEmpty(request.Password))
            {
                return BadRequest(new { error = "Email and password are required" });
            }

            if (string.IsNullOrEmpty(request.PhoneNumber))
            {
                return BadRequest(new { error = "Phone number is required" });
            }

            // Validate password strength
            if (request.Password.Length < 8)
            {
                return BadRequest(new { error = "Password must be at least 8 characters long" });
            }

            // Check if user already exists
            var existingUser = await _userService.GetUserByEmailAsync(request.Email);
            if (existingUser != null)
            {
                return Conflict(new { error = "User with this email already exists" });
            }

            // Create user
            var user = await _userService.CreateUserAsync(request);

            // Generate and send OTP
            var otpCode = await _otpService.GenerateAndStoreOtpAsync(request.Email, "signup", user.Id);
            var otpSent = await _otpService.SendOtpAsync(request.PhoneNumber, otpCode);

            if (!otpSent)
            {
                _logger.LogWarning("Failed to send OTP to phone: {PhoneNumber}", request.PhoneNumber);
                return StatusCode(500, new { error = "Failed to send verification code" });
            }

            var userResponse = await _userService.MapToUserResponseAsync(user);
            return CreatedAtAction(nameof(Signup), new { id = user.Id }, new {
                message = "User created successfully. Please verify your phone number.",
                user = userResponse
            });
        }
        catch (InvalidOperationException ex)
        {
            return Conflict(new { error = ex.Message });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during signup for email: {Email}", request.Email);
            return StatusCode(500, new { error = "An error occurred during signup" });
        }
    }

    [HttpPost("login")]
    public async Task<IActionResult> Login([FromBody] LoginRequest request)
    {
        try
        {
            if (string.IsNullOrEmpty(request.Email) || string.IsNullOrEmpty(request.Password))
            {
                return BadRequest(new { error = "Email and password are required" });
            }

            // Validate credentials
            var isValidPassword = await _userService.ValidatePasswordAsync(request.Email, request.Password);
            if (!isValidPassword)
            {
                return Unauthorized(new { error = "Invalid credentials" });
            }

            // Get user details
            var user = await _userService.GetUserByEmailAsync(request.Email);
            if (user == null || !user.IsActive)
            {
                return Unauthorized(new { error = "Account not found or inactive" });
            }

            // Check if user is verified
            if (!user.IsVerified)
            {
                return Unauthorized(new { error = "Please verify your account first" });
            }

            // Generate JWT token
            var token = _jwtService.GenerateToken(user.Id.ToString(), user.Role);
            var userResponse = await _userService.MapToUserResponseAsync(user);

            return Ok(new LoginResponse(token, userResponse));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during login for email: {Email}", request.Email);
            return StatusCode(500, new { error = "An error occurred during login" });
        }
    }

    [HttpPost("verify-otp")]
    public async Task<IActionResult> VerifyOtp([FromBody] OtpRequest request)
    {
        try
        {
            if (string.IsNullOrEmpty(request.Email) || string.IsNullOrEmpty(request.OtpCode))
            {
                return BadRequest(new { error = "Email and OTP code are required" });
            }

            // Validate OTP
            var isValidOtp = await _otpService.ValidateOtpAsync(request.Email, request.OtpCode, "signup");
            if (!isValidOtp)
            {
                return BadRequest(new { error = "Invalid or expired OTP" });
            }

            // Mark user as verified
            var updated = await _userService.UpdateUserVerificationAsync(request.Email, true);
            if (!updated)
            {
                return BadRequest(new { error = "User not found" });
            }

            return Ok(new {
                success = true,
                message = "Account verified successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during OTP verification for email: {Email}", request.Email);
            return StatusCode(500, new { error = "An error occurred during verification" });
        }
    }

    [HttpGet("roles")]
    public IActionResult GetRoles()
    {
        var roles = new[] { "personal", "merchant" };
        return Ok(new { roles });
    }

    [HttpPost("resend-otp")]
    public async Task<IActionResult> ResendOtp([FromBody] ResendOtpRequest request)
    {
        try
        {
            if (string.IsNullOrEmpty(request.Email))
            {
                return BadRequest(new { error = "Email is required" });
            }

            var user = await _userService.GetUserByEmailAsync(request.Email);
            if (user == null)
            {
                return BadRequest(new { error = "User not found" });
            }

            if (user.IsVerified)
            {
                return BadRequest(new { error = "Account is already verified" });
            }

            // Generate and send new OTP
            var otpCode = await _otpService.GenerateAndStoreOtpAsync(request.Email, "signup", user.Id);
            var otpSent = await _otpService.SendOtpAsync(user.PhoneNumber!, otpCode);

            if (!otpSent)
            {
                return StatusCode(500, new { error = "Failed to send verification code" });
            }

            return Ok(new { message = "Verification code sent successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resending OTP for email: {Email}", request.Email);
            return StatusCode(500, new { error = "An error occurred while resending OTP" });
        }
    }
}

public record ResendOtpRequest(string Email);
