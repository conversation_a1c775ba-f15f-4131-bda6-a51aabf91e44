use std::collections::HashMap;
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use tracing::{info, warn};

use crate::config::Config;
use crate::error::{AppError, Result};

#[derive(Debug, Clone)]
pub struct MpesaService {
    client: reqwest::Client,
    api_key: String,
    username: String,
    base_url: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MpesaPaymentRequest {
    pub recipient_phone: String,
    pub amount: String,
    pub currency: String,
    pub reference: String,
    pub description: String,
    pub callback_url: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MpesaPaymentResponse {
    pub transaction_id: String,
    pub status: String,
    pub message: String,
    pub recipient_phone: String,
    pub amount: String,
    pub reference: String,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MpesaStatusResponse {
    pub transaction_id: String,
    pub status: String,
    pub amount: String,
    pub recipient_phone: String,
    pub completed_at: Option<DateTime<Utc>>,
    pub failure_reason: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MpesaWebhookPayload {
    pub transaction_id: String,
    pub status: String,
    pub amount: String,
    pub recipient_phone: String,
    pub reference: String,
    pub completed_at: Option<DateTime<Utc>>,
    pub failure_reason: Option<String>,
}

impl MpesaService {
    pub fn new(config: &Config) -> Self {
        Self {
            client: reqwest::Client::new(),
            api_key: config.africas_talking_api_key.clone(),
            username: config.africas_talking_username.clone(),
            base_url: "https://payments.africastalking.com".to_string(),
        }
    }

    pub async fn send_payment(&self, request: MpesaPaymentRequest) -> Result<MpesaPaymentResponse> {
        info!("Sending M-Pesa payment to {}: {} KES", request.recipient_phone, request.amount);

        // For mock implementation, return a mock response
        if self.api_key == "mock-at-api-key" {
            return self.create_mock_payment_response(&request);
        }

        // Real Africa's Talking M-Pesa integration
        match self.send_real_payment(&request).await {
            Ok(response) => Ok(response),
            Err(e) => {
                warn!("Real M-Pesa payment failed: {}, using mock response", e);
                self.create_mock_payment_response(&request)
            }
        }
    }

    async fn send_real_payment(&self, request: &MpesaPaymentRequest) -> Result<MpesaPaymentResponse> {
        let url = format!("{}/mobile/b2c/request", self.base_url);

        let mut form_data = HashMap::new();
        form_data.insert("username", self.username.clone());
        form_data.insert("productName", "Qpesapay".to_string());
        form_data.insert("recipients", format!(
            "[{{\"phoneNumber\":\"{}\",\"amount\":\"{}\",\"currencyCode\":\"KES\",\"reason\":\"BusinessPayment\",\"metadata\":{{\"reference\":\"{}\"}}}}]",
            request.recipient_phone,
            request.amount,
            request.reference
        ));

        let response = self
            .client
            .post(&url)
            .header("apiKey", &self.api_key)
            .header("Content-Type", "application/x-www-form-urlencoded")
            .form(&form_data)
            .send()
            .await
            .map_err(AppError::Http)?;

        if !response.status().is_success() {
            return Err(AppError::ExternalApi(format!(
                "Africa's Talking API error: {}",
                response.status()
            )));
        }

        let response_text = response.text().await.map_err(AppError::Http)?;
        info!("Africa's Talking response: {}", response_text);

        // Parse the response (Africa's Talking has a specific format)
        // For now, create a mock response based on the request
        self.create_mock_payment_response(request)
    }

    fn create_mock_payment_response(&self, request: &MpesaPaymentRequest) -> Result<MpesaPaymentResponse> {
        let transaction_id = format!("MP{}",
            chrono::Utc::now().timestamp()
        );

        info!("Mock M-Pesa payment created: {} for {} KES to {}", 
            transaction_id, request.amount, request.recipient_phone);

        Ok(MpesaPaymentResponse {
            transaction_id,
            status: "pending".to_string(),
            message: "Payment request submitted successfully".to_string(),
            recipient_phone: request.recipient_phone.clone(),
            amount: request.amount.clone(),
            reference: request.reference.clone(),
            created_at: Utc::now(),
        })
    }

    pub async fn check_payment_status(&self, transaction_id: &str) -> Result<MpesaStatusResponse> {
        info!("Checking M-Pesa payment status: {}", transaction_id);

        // For mock implementation, return completed status
        if self.api_key == "mock-at-api-key" {
            return Ok(MpesaStatusResponse {
                transaction_id: transaction_id.to_string(),
                status: "completed".to_string(),
                amount: "1000.00".to_string(),
                recipient_phone: "+254700000000".to_string(),
                completed_at: Some(Utc::now()),
                failure_reason: None,
            });
        }

        // Real status check would go here
        // For now, return mock response
        Ok(MpesaStatusResponse {
            transaction_id: transaction_id.to_string(),
            status: "completed".to_string(),
            amount: "1000.00".to_string(),
            recipient_phone: "+254700000000".to_string(),
            completed_at: Some(Utc::now()),
            failure_reason: None,
        })
    }

    pub async fn handle_webhook(&self, payload: serde_json::Value) -> Result<MpesaWebhookPayload> {
        info!("Processing M-Pesa webhook: {:?}", payload);

        // Parse webhook payload
        let webhook: MpesaWebhookPayload = serde_json::from_value(payload)
            .map_err(|e| AppError::Parse(format!("Invalid M-Pesa webhook payload: {}", e)))?;

        info!("M-Pesa webhook processed: {} - {}", webhook.transaction_id, webhook.status);
        Ok(webhook)
    }

    pub async fn validate_phone_number(&self, phone: &str) -> Result<String> {
        // Basic Kenyan phone number validation and normalization
        let cleaned = phone.replace(" ", "").replace("-", "");
        
        if cleaned.starts_with("+254") && cleaned.len() == 13 {
            Ok(cleaned)
        } else if cleaned.starts_with("254") && cleaned.len() == 12 {
            Ok(format!("+{}", cleaned))
        } else if cleaned.starts_with("0") && cleaned.len() == 10 {
            Ok(format!("+254{}", &cleaned[1..]))
        } else if cleaned.len() == 9 {
            Ok(format!("+254{}", cleaned))
        } else {
            Err(AppError::Validation(format!("Invalid Kenyan phone number: {}", phone)))
        }
    }

    pub async fn get_balance(&self) -> Result<serde_json::Value> {
        info!("Getting M-Pesa account balance");

        // For mock implementation
        if self.api_key == "mock-at-api-key" {
            return Ok(serde_json::json!({
                "balance": "50000.00",
                "currency": "KES",
                "last_updated": Utc::now()
            }));
        }

        // Real balance check would go here
        Ok(serde_json::json!({
            "balance": "50000.00",
            "currency": "KES",
            "last_updated": Utc::now()
        }))
    }

    pub async fn get_transaction_history(&self, limit: Option<u32>) -> Result<Vec<serde_json::Value>> {
        info!("Getting M-Pesa transaction history (limit: {:?})", limit);

        // For mock implementation, return empty history
        Ok(vec![])
    }

    pub fn format_amount(&self, amount: Decimal) -> String {
        // Format amount to 2 decimal places for M-Pesa
        format!("{:.2}", amount)
    }

    pub fn generate_reference(&self, settlement_id: &str) -> String {
        format!("QPESA_{}", settlement_id.replace("-", "")[..8].to_uppercase())
    }
}
