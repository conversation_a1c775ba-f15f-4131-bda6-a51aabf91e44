using System.Net.Http.Json;
using Microsoft.EntityFrameworkCore;
using Qpesapay.AuthService.Data;
using Qpesapay.AuthService.Models;

namespace Qpesapay.AuthService.Services;

public interface IOtpService
{
    Task<string> GenerateAndStoreOtpAsync(string email, string purpose, Guid? userId = null);
    Task<bool> ValidateOtpAsync(string email, string otpCode, string purpose);
    Task<bool> SendOtpAsync(string phoneNumber, string otpCode);
    Task CleanupExpiredOtpsAsync();
}

public class OtpService : IOtpService
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IConfiguration _configuration;
    private readonly AuthDbContext _context;
    private readonly ILogger<OtpService> _logger;

    public OtpService(
        IHttpClientFactory httpClientFactory,
        IConfiguration configuration,
        AuthDbContext context,
        ILogger<OtpService> logger)
    {
        _httpClientFactory = httpClientFactory;
        _configuration = configuration;
        _context = context;
        _logger = logger;
    }

    public async Task<string> GenerateAndStoreOtpAsync(string email, string purpose, Guid? userId = null)
    {
        try
        {
            // Generate 6-digit OTP
            var otpCode = Random.Shared.Next(100000, 999999).ToString();

            // Clean up any existing unused OTPs for this email and purpose
            var existingOtps = await _context.OtpCodes
                .Where(o => o.Email == email && o.Purpose == purpose && o.UsedAt == null)
                .ToListAsync();

            _context.OtpCodes.RemoveRange(existingOtps);

            // Store new OTP
            var otpEntity = new OtpCode
            {
                UserId = userId,
                Email = email.ToLower().Trim(),
                Code = otpCode,
                Purpose = purpose,
                ExpiresAt = DateTime.UtcNow.AddMinutes(10), // 10 minutes expiry
                CreatedAt = DateTime.UtcNow
            };

            _context.OtpCodes.Add(otpEntity);
            await _context.SaveChangesAsync();

            _logger.LogInformation("OTP generated for email: {Email}, purpose: {Purpose}", email, purpose);
            return otpCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating OTP for email: {Email}", email);
            throw;
        }
    }

    public async Task<bool> ValidateOtpAsync(string email, string otpCode, string purpose)
    {
        try
        {
            var otp = await _context.OtpCodes
                .FirstOrDefaultAsync(o =>
                    o.Email == email.ToLower().Trim() &&
                    o.Code == otpCode &&
                    o.Purpose == purpose &&
                    o.UsedAt == null);

            if (otp == null)
            {
                _logger.LogWarning("Invalid OTP attempt for email: {Email}, purpose: {Purpose}", email, purpose);
                return false;
            }

            if (otp.ExpiresAt < DateTime.UtcNow)
            {
                _logger.LogWarning("Expired OTP attempt for email: {Email}, purpose: {Purpose}", email, purpose);
                return false;
            }

            // Mark OTP as used
            otp.UsedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();

            _logger.LogInformation("OTP validated successfully for email: {Email}, purpose: {Purpose}", email, purpose);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating OTP for email: {Email}", email);
            return false;
        }
    }

    public async Task<bool> SendOtpAsync(string phoneNumber, string otpCode)
    {
        var provider = _configuration["OtpSettings:Provider"];

        try
        {
            return provider?.ToLower() switch
            {
                "africatalking" => await SendOtpViaAfricaTalking(phoneNumber, otpCode),
                "twilio" => await SendOtpViaTwilio(phoneNumber, otpCode),
                _ => throw new NotSupportedException($"OTP provider {provider} is not supported")
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending OTP to phone: {PhoneNumber}", phoneNumber);
            return false;
        }
    }

    public async Task CleanupExpiredOtpsAsync()
    {
        try
        {
            var expiredOtps = await _context.OtpCodes
                .Where(o => o.ExpiresAt < DateTime.UtcNow)
                .ToListAsync();

            if (expiredOtps.Any())
            {
                _context.OtpCodes.RemoveRange(expiredOtps);
                await _context.SaveChangesAsync();
                _logger.LogInformation("Cleaned up {Count} expired OTPs", expiredOtps.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up expired OTPs");
        }
    }

    private async Task<bool> SendOtpViaAfricaTalking(string phoneNumber, string otpCode)
    {
        var apiKey = _configuration["OtpSettings:ApiKey"];
        var username = _configuration["OtpSettings:Username"];

        if (string.IsNullOrEmpty(apiKey) || string.IsNullOrEmpty(username))
        {
            _logger.LogWarning("Africa's Talking credentials not configured");
            return false;
        }

        var client = _httpClientFactory.CreateClient();
        client.DefaultRequestHeaders.Add("apiKey", apiKey);
        client.DefaultRequestHeaders.Add("Accept", "application/json");

        var requestData = new Dictionary<string, string>
        {
            ["username"] = username,
            ["to"] = phoneNumber,
            ["message"] = $"Your Qpesapay verification code is: {otpCode}. Valid for 10 minutes."
        };

        // Use sandbox endpoint for sandbox username
        var endpoint = username.ToLower() == "sandbox"
            ? "https://api.sandbox.africastalking.com/version1/messaging"
            : "https://api.africastalking.com/version1/messaging";

        var response = await client.PostAsync(endpoint, new FormUrlEncodedContent(requestData));

        var success = response.IsSuccessStatusCode;
        var responseContent = await response.Content.ReadAsStringAsync();

        if (!success)
        {
            _logger.LogError("Africa's Talking API error - Status: {StatusCode}, Response: {Response}",
                response.StatusCode, responseContent);
        }
        else
        {
            _logger.LogInformation("Africa's Talking API success - Response: {Response}", responseContent);
        }

        return success;
    }

    private async Task<bool> SendOtpViaTwilio(string phoneNumber, string otpCode)
    {
        // Twilio implementation would go here
        // This is a placeholder for the actual Twilio integration
        _logger.LogInformation("Twilio OTP sending not implemented yet");
        return await Task.FromResult(true);
    }
}
