#!/usr/bin/env pwsh

<#
.SYNOPSIS
    Deploy the complete Qpesapay crypto-fiat payment system
.DESCRIPTION
    This script deploys all microservices, database, and supporting infrastructure
    for the Qpesapay ecosystem using Docker Compose.
.PARAMETER Clean
    Clean up existing containers and volumes before deployment
.PARAMETER Logs
    Show logs after deployment
.PARAMETER SkipBuild
    Skip building images and use existing ones
#>

param(
    [switch]$Clean,
    [switch]$Logs,
    [switch]$SkipBuild
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Colors for output
$Green = "`e[32m"
$Yellow = "`e[33m"
$Red = "`e[31m"
$Blue = "`e[34m"
$Reset = "`e[0m"

function Write-ColorOutput {
    param([string]$Message, [string]$Color = $Reset)
    Write-Host "$Color$Message$Reset"
}

function Test-DockerRunning {
    try {
        docker info | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

function Test-DockerComposeFile {
    if (-not (Test-Path "docker-compose.yml")) {
        Write-ColorOutput "❌ docker-compose.yml not found in current directory" $Red
        exit 1
    }
}

function Stop-ExistingServices {
    Write-ColorOutput "🛑 Stopping existing services..." $Yellow
    docker-compose down --remove-orphans
    
    if ($Clean) {
        Write-ColorOutput "🧹 Cleaning up volumes and images..." $Yellow
        docker-compose down -v --rmi all
        docker system prune -f
    }
}

function Build-Services {
    if ($SkipBuild) {
        Write-ColorOutput "⏭️  Skipping build step..." $Yellow
        return
    }
    
    Write-ColorOutput "🔨 Building all services..." $Blue
    docker-compose build --parallel
    
    if ($LASTEXITCODE -ne 0) {
        Write-ColorOutput "❌ Build failed!" $Red
        exit 1
    }
}

function Start-Services {
    Write-ColorOutput "🚀 Starting all services..." $Green
    docker-compose up -d
    
    if ($LASTEXITCODE -ne 0) {
        Write-ColorOutput "❌ Failed to start services!" $Red
        exit 1
    }
}

function Wait-ForServices {
    Write-ColorOutput "⏳ Waiting for services to be healthy..." $Yellow
    
    $services = @("postgres", "redis", "auth-service", "kyc-service")
    $maxWait = 120 # seconds
    $waited = 0
    
    while ($waited -lt $maxWait) {
        $allHealthy = $true
        
        foreach ($service in $services) {
            $health = docker-compose ps --format json | ConvertFrom-Json | Where-Object { $_.Service -eq $service } | Select-Object -ExpandProperty Health -ErrorAction SilentlyContinue
            
            if ($health -ne "healthy" -and $health -ne "running") {
                $allHealthy = $false
                break
            }
        }
        
        if ($allHealthy) {
            Write-ColorOutput "✅ All core services are healthy!" $Green
            break
        }
        
        Start-Sleep -Seconds 5
        $waited += 5
        Write-Host "." -NoNewline
    }
    
    if ($waited -ge $maxWait) {
        Write-ColorOutput "`n⚠️  Some services may not be fully ready yet" $Yellow
    }
}

function Show-ServiceStatus {
    Write-ColorOutput "`n📊 Service Status:" $Blue
    docker-compose ps
    
    Write-ColorOutput "`n🌐 Service URLs:" $Blue
    Write-ColorOutput "• Auth Service:       http://localhost:5001" $Green
    Write-ColorOutput "• KYC Service:        http://localhost:5002" $Green
    Write-ColorOutput "• Wallet Service:     http://localhost:8001" $Green
    Write-ColorOutput "• Blockchain Listener: http://localhost:8002" $Green
    Write-ColorOutput "• Settlement Service: http://localhost:8003" $Green
    Write-ColorOutput "• PostgreSQL:         localhost:5432" $Green
    Write-ColorOutput "• Redis:              localhost:6379" $Green
}

function Show-Logs {
    if ($Logs) {
        Write-ColorOutput "`n📋 Service Logs (last 50 lines):" $Blue
        docker-compose logs --tail=50
    }
}

function Test-ServiceEndpoints {
    Write-ColorOutput "`n🔍 Testing service endpoints..." $Blue
    
    $endpoints = @{
        "Auth Service" = "http://localhost:5001/health"
        "KYC Service" = "http://localhost:5002/health"
    }
    
    foreach ($service in $endpoints.Keys) {
        try {
            $response = Invoke-WebRequest -Uri $endpoints[$service] -TimeoutSec 10 -ErrorAction Stop
            if ($response.StatusCode -eq 200) {
                Write-ColorOutput "✅ $service is responding" $Green
            }
        }
        catch {
            Write-ColorOutput "⚠️  $service may not be ready yet" $Yellow
        }
    }
}

# Main execution
Write-ColorOutput "🚀 Qpesapay System Deployment Script" $Blue
Write-ColorOutput "=====================================" $Blue

# Pre-flight checks
if (-not (Test-DockerRunning)) {
    Write-ColorOutput "❌ Docker is not running. Please start Docker and try again." $Red
    exit 1
}

Test-DockerComposeFile

# Deployment steps
Stop-ExistingServices
Build-Services
Start-Services
Wait-ForServices
Show-ServiceStatus
Test-ServiceEndpoints
Show-Logs

Write-ColorOutput "`n🎉 Qpesapay system deployment complete!" $Green
Write-ColorOutput "💡 Use 'docker-compose logs -f [service-name]' to view real-time logs" $Yellow
Write-ColorOutput "💡 Use 'docker-compose down' to stop all services" $Yellow
