use axum::{
    extract::{Path, Query, State},
    response::Json,
    routing::{get, post},
    Router,
};
use serde::Deserialize;
use std::sync::Arc;
use tokio::signal;
use tower_http::cors::CorsLayer;
use tracing::{info, error};
use uuid::Uuid;

mod config;
mod database;
mod error;
mod models;
mod services;

use config::Config;
use database::Database;
use error::{AppError, Result};
use models::*;
use services::{YellowcardService, SettlementProcessor};

#[derive(Clone)]
pub struct AppState {
    pub db: Database,
    pub yellowcard_service: Arc<YellowcardService>,
    pub settlement_processor: Arc<SettlementProcessor>,
    pub config: Config,
}

#[derive(Deserialize)]
struct ExchangeRateQuery {
    from_currency: String,
    to_currency: String,
}

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize tracing
    tracing_subscriber::fmt::init();

    // Load configuration
    let config = Config::from_env()?;
    info!("Starting Qpesapay Settlement Service");

    // Initialize database
    let db = Database::new(&config.database_url).await?;
    db.migrate().await?;

    // Initialize services
    let yellowcard_service = Arc::new(YellowcardService::new(&config));
    let settlement_processor = Arc::new(SettlementProcessor::new(
        db.clone(),
        yellowcard_service.clone(),
        config.clone(),
    ));

    // Create application state
    let state = AppState {
        db,
        yellowcard_service,
        settlement_processor: settlement_processor.clone(),
        config: config.clone(),
    };

    // Start settlement processor
    let processor_handle = tokio::spawn(async move {
        if let Err(e) = settlement_processor.start().await {
            error!("Settlement processor error: {}", e);
        }
    });

    // Build HTTP server
    let app = Router::new()
        .route("/health", get(health_check))
        .route("/exchange-rate", get(get_exchange_rate))
        .route("/settlements", post(create_settlement))
        .route("/settlements/:id", get(get_settlement))
        .route("/settlements/:id/status", get(get_settlement_status))
        .route("/webhook/yellowcard", post(yellowcard_webhook))
        .layer(CorsLayer::permissive())
        .with_state(state);

    // Start HTTP server
    let listener = tokio::net::TcpListener::bind(format!("0.0.0.0:{}", config.port))
        .await
        .unwrap();
    
    info!("Settlement service listening on port {}", config.port);
    
    let server_handle = tokio::spawn(async move {
        axum::serve(listener, app).await.unwrap();
    });

    // Wait for shutdown signal
    tokio::select! {
        _ = signal::ctrl_c() => {
            info!("Received shutdown signal");
        }
        _ = processor_handle => {
            error!("Settlement processor stopped unexpectedly");
        }
        _ = server_handle => {
            error!("HTTP server stopped unexpectedly");
        }
    }

    info!("Shutting down settlement service");
    Ok(())
}

// Get exchange rate endpoint
async fn get_exchange_rate(
    State(state): State<AppState>,
    Query(params): Query<ExchangeRateQuery>,
) -> std::result::Result<Json<serde_json::Value>, AppError> {
    let rate = state.settlement_processor.get_exchange_rate(&params.from_currency, &params.to_currency).await?;

    Ok(Json(serde_json::json!({
        "from_currency": rate.from_currency,
        "to_currency": rate.to_currency,
        "rate": rate.rate,
        "source": rate.source,
        "timestamp": rate.created_at,
        "expires_at": rate.expires_at
    })))
}

// Health check endpoint
async fn health_check(State(_state): State<AppState>) -> Json<serde_json::Value> {
    Json(serde_json::json!({
        "status": "healthy",
        "service": "qpesapay-settlement-service",
        "timestamp": chrono::Utc::now(),
        "database": "connected"
    }))
}

// Create settlement request
async fn create_settlement(
    State(state): State<AppState>,
    Json(request): Json<CreateSettlementRequest>,
) -> std::result::Result<Json<models::SettlementResponse>, AppError> {
    let settlement = state.settlement_processor.create_settlement(request).await?;
    Ok(Json(settlement))
}

// Get settlement by ID
async fn get_settlement(
    State(state): State<AppState>,
    Path(settlement_id): Path<Uuid>,
) -> std::result::Result<Json<models::SettlementResponse>, AppError> {
    let settlement = state.db.get_settlement_by_id(settlement_id).await?
        .ok_or_else(|| AppError::NotFound("Settlement not found".to_string()))?;
    
    Ok(Json(settlement.into()))
}

// Get settlement status
async fn get_settlement_status(
    State(state): State<AppState>,
    Path(settlement_id): Path<Uuid>,
) -> std::result::Result<Json<models::SettlementStatusResponse>, AppError> {
    let settlement = state.db.get_settlement_by_id(settlement_id).await?
        .ok_or_else(|| AppError::NotFound("Settlement not found".to_string()))?;

    Ok(Json(settlement.into()))
}

// Yellowcard webhook endpoint
async fn yellowcard_webhook(
    State(state): State<AppState>,
    Json(payload): Json<serde_json::Value>,
) -> std::result::Result<Json<serde_json::Value>, AppError> {
    state.yellowcard_service.handle_webhook(payload).await?;
    
    Ok(Json(serde_json::json!({
        "status": "received",
        "timestamp": chrono::Utc::now()
    })))
}
