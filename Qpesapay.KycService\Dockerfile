# Use the official .NET runtime as a parent image
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

# Use the SDK image to build the application
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["Qpesapay.KycService.csproj", "."]
RUN dotnet restore "Qpesapay.KycService.csproj"
COPY . .
WORKDIR "/src"
RUN dotnet build "Qpesapay.KycService.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "Qpesapay.KycService.csproj" -c Release -o /app/publish

# Build runtime image
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

# Create uploads directory
RUN mkdir -p /app/uploads && chmod 755 /app/uploads

ENTRYPOINT ["dotnet", "Qpesapay.KycService.dll"]
