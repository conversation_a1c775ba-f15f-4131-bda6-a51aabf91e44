use axum::{
    extract::State,
    http::StatusCode,
    response::Json,
    routing::get,
    Router,
};
use std::sync::Arc;
use tokio::signal;
use tower_http::cors::CorsLayer;
use tracing::{info, error};

mod config;
mod database;
mod error;
mod listeners;
mod models;
mod services;

use config::Config;
use database::Database;
use error::Result;
use listeners::{BitcoinListener, EthereumListener, TronListener, BlockchainListener};
use services::NotificationService;

#[derive(Clone)]
pub struct AppState {
    pub db: Database,
    pub notification_service: Arc<NotificationService>,
    pub config: Config,
}

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize tracing
    tracing_subscriber::fmt::init();

    // Load configuration
    let config = Config::from_env()?;
    info!("Starting Qpesapay Blockchain Listener Service");

    // Initialize database
    let db = Database::new(&config.database_url).await?;
    db.migrate().await?;

    // Initialize notification service
    let notification_service = Arc::new(NotificationService::new(&config));

    // Create application state
    let state = AppState {
        db: db.clone(),
        notification_service: notification_service.clone(),
        config: config.clone(),
    };

    // Start blockchain listeners
    let bitcoin_listener = BitcoinListener::new(
        config.bitcoin_rpc_url.clone(),
        config.bitcoin_rpc_user.clone(),
        config.bitcoin_rpc_password.clone(),
        db.clone(),
        notification_service.clone(),
    );

    let ethereum_listener = EthereumListener::new(
        config.ethereum_ws_url.clone(),
        config.usdt_erc20_contract.clone(),
        db.clone(),
        notification_service.clone(),
    );

    let tron_listener = TronListener::new(
        config.tron_api_url.clone(),
        config.usdt_trc20_contract.clone(),
        db.clone(),
        notification_service.clone(),
    );

    // Start listeners in background tasks
    let bitcoin_handle = tokio::spawn(async move {
        if let Err(e) = bitcoin_listener.start().await {
            error!("Bitcoin listener error: {}", e);
        }
    });

    let ethereum_handle = tokio::spawn(async move {
        if let Err(e) = ethereum_listener.start().await {
            error!("Ethereum listener error: {}", e);
        }
    });

    let tron_handle = tokio::spawn(async move {
        if let Err(e) = tron_listener.start().await {
            error!("TRON listener error: {}", e);
        }
    });

    // Build HTTP server for health checks and metrics
    let app = Router::new()
        .route("/health", get(health_check))
        .route("/metrics", get(metrics))
        .layer(CorsLayer::permissive())
        .with_state(state);

    // Start HTTP server
    let listener = tokio::net::TcpListener::bind(format!("0.0.0.0:{}", config.port))
        .await
        .unwrap();
    
    info!("Blockchain listener service listening on port {}", config.port);
    
    let server_handle = tokio::spawn(async move {
        axum::serve(listener, app).await.unwrap();
    });

    // Wait for shutdown signal
    tokio::select! {
        _ = signal::ctrl_c() => {
            info!("Received shutdown signal");
        }
        _ = bitcoin_handle => {
            error!("Bitcoin listener stopped unexpectedly");
        }
        _ = ethereum_handle => {
            error!("Ethereum listener stopped unexpectedly");
        }
        _ = tron_handle => {
            error!("TRON listener stopped unexpectedly");
        }
        _ = server_handle => {
            error!("HTTP server stopped unexpectedly");
        }
    }

    info!("Shutting down blockchain listener service");
    Ok(())
}

// Health check endpoint
async fn health_check(State(state): State<AppState>) -> Json<serde_json::Value> {
    Json(serde_json::json!({
        "status": "healthy",
        "service": "qpesapay-blockchain-listener",
        "timestamp": chrono::Utc::now(),
        "database": "connected"
    }))
}

// Metrics endpoint
async fn metrics(State(_state): State<AppState>) -> std::result::Result<String, StatusCode> {
    // TODO: Implement Prometheus metrics
    Ok("# Blockchain Listener Metrics\n# TODO: Implement metrics\n".to_string())
}
