{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from typing import List\n", "from integuru.graph_builder import build_graph\n", "from integuru.util.LLM import llm\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv()\n", "\n", "agent = None\n", "\n", "async def call_agent(\n", "    model: str,\n", "    prompt: str,\n", "    max_steps: int = 10,\n", "    har_file_path: str = \"turbo.har\",\n", "    cookie_path: str = \"turbo.json\",\n", "    input_variables: dict = None,\n", "):  \n", "    \n", "    llm.set_default_model(model)\n", "    global agent\n", "    graph, agent = build_graph(prompt, har_file_path, cookie_path)\n", "    event_stream = graph.astream(\n", "        {\n", "            \"master_node\": None,\n", "            \"in_process_node\": None,\n", "            \"to_be_processed_nodes\": [],\n", "            \"in_process_node_dynamic_parts\": [],\n", "            \"action_url\": \"\",\n", "            \"input_variables\": input_variables or {},  \n", "        },\n", "        {\n", "            \"recursion_limit\": max_steps,\n", "        },\n", "    )\n", "    async for event in event_stream:\n", "        print(\"+++\", event)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "model = \"gpt-4o\"\n", "prompt = \"Download my bank statement file.\"\n", "input_variables = {\n", "}\n", "har_path = \"network_requests.har\"\n", "cookie_path = \"cookies.json\" \n", "max_steps = 15\n", "await call_agent(model=model, prompt=prompt, har_file_path=har_path, cookie_path=cookie_path, max_steps=max_steps, input_variables=input_variables)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from integuru.util.print import *\n", "\n", "print_dag(agent.dag_manager.graph, agent.global_master_node_id)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print_dag_in_reverse(agent.dag_manager.graph, to_generate_code=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "integuru", "language": "python", "name": "integuru"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}