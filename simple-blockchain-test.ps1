# Simple Blockchain Integration Test
Write-Host "Qpesapay Blockchain Integration Test" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green

$testUserId = "550e8400-e29b-41d4-a716-446655440000"
$headers = @{'Content-Type' = 'application/json'}

# Test 1: Create Bitcoin Wallet
Write-Host "`nTest 1: Creating Bitcoin Wallet..." -ForegroundColor Yellow
$btcBody = "{`"user_id`": `"$testUserId`", `"currency`": `"BTC`"}"
try {
    $response = Invoke-WebRequest -Uri 'http://localhost:5003/wallets' -Method POST -Headers $headers -Body $btcBody -UseBasicParsing
    $btcWallet = $response.Content | ConvertFrom-Json
    Write-Host "SUCCESS: Bitcoin wallet created!" -ForegroundColor Green
    Write-Host "Address: $($btcWallet.address)" -ForegroundColor Cyan
} catch {
    if ($_.Exception.Message -like "*already exists*") {
        Write-Host "WARNING: Bitcoin wallet already exists - continuing..." -ForegroundColor Yellow
        $response = Invoke-WebRequest -Uri "http://localhost:5003/wallets/$testUserId" -UseBasicParsing
        $wallets = $response.Content | ConvertFrom-Json
        $btcWallet = $wallets | Where-Object { $_.currency -eq "BTC" } | Select-Object -First 1
        Write-Host "Address: $($btcWallet.address)" -ForegroundColor Cyan
    } else {
        Write-Host "FAILED: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test 2: Create Ethereum USDT Wallet
Write-Host "`nTest 2: Creating Ethereum USDT Wallet..." -ForegroundColor Yellow
$ethBody = "{`"user_id`": `"$testUserId`", `"currency`": `"USDT_ERC20`"}"
try {
    $response = Invoke-WebRequest -Uri 'http://localhost:5003/wallets' -Method POST -Headers $headers -Body $ethBody -UseBasicParsing
    $ethWallet = $response.Content | ConvertFrom-Json
    Write-Host "SUCCESS: Ethereum USDT wallet created!" -ForegroundColor Green
    Write-Host "Address: $($ethWallet.address)" -ForegroundColor Cyan
} catch {
    if ($_.Exception.Message -like "*already exists*") {
        Write-Host "WARNING: Ethereum USDT wallet already exists - continuing..." -ForegroundColor Yellow
        $response = Invoke-WebRequest -Uri "http://localhost:5003/wallets/$testUserId" -UseBasicParsing
        $wallets = $response.Content | ConvertFrom-Json
        $ethWallet = $wallets | Where-Object { $_.currency -eq "USDT_ERC20" } | Select-Object -First 1
        Write-Host "Address: $($ethWallet.address)" -ForegroundColor Cyan
    } else {
        Write-Host "FAILED: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test 3: Create TRON USDT Wallet
Write-Host "`nTest 3: Creating TRON USDT Wallet..." -ForegroundColor Yellow
$tronBody = "{`"user_id`": `"$testUserId`", `"currency`": `"USDT_TRC20`"}"
try {
    $response = Invoke-WebRequest -Uri 'http://localhost:5003/wallets' -Method POST -Headers $headers -Body $tronBody -UseBasicParsing
    $tronWallet = $response.Content | ConvertFrom-Json
    Write-Host "SUCCESS: TRON USDT wallet created!" -ForegroundColor Green
    Write-Host "Address: $($tronWallet.address)" -ForegroundColor Cyan
} catch {
    if ($_.Exception.Message -like "*already exists*") {
        Write-Host "WARNING: TRON USDT wallet already exists - continuing..." -ForegroundColor Yellow
        $response = Invoke-WebRequest -Uri "http://localhost:5003/wallets/$testUserId" -UseBasicParsing
        $wallets = $response.Content | ConvertFrom-Json
        $tronWallet = $wallets | Where-Object { $_.currency -eq "USDT_TRC20" } | Select-Object -First 1
        Write-Host "Address: $($tronWallet.address)" -ForegroundColor Cyan
    } else {
        Write-Host "FAILED: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test 4: Check Blockchain Listener Health
Write-Host "`nTest 4: Checking Blockchain Listener Health..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri 'http://localhost:5004/health' -UseBasicParsing
    $health = $response.Content | ConvertFrom-Json
    Write-Host "SUCCESS: Blockchain Listener is healthy!" -ForegroundColor Green
    Write-Host "Status: $($health.status)" -ForegroundColor Cyan
    Write-Host "Database: $($health.database)" -ForegroundColor Cyan
} catch {
    Write-Host "FAILED: Blockchain Listener health check failed" -ForegroundColor Red
}

# Test 5: Check Service Integration
Write-Host "`nTest 5: Checking All Services..." -ForegroundColor Yellow
$services = @(
    @{Name="Auth Service"; Url="http://localhost:5001/health"},
    @{Name="Wallet Service"; Url="http://localhost:5003/health"},
    @{Name="Blockchain Listener"; Url="http://localhost:5004/health"}
)

foreach ($service in $services) {
    try {
        $response = Invoke-WebRequest -Uri $service.Url -UseBasicParsing -TimeoutSec 5
        Write-Host "SUCCESS: $($service.Name) is healthy" -ForegroundColor Green
    } catch {
        Write-Host "FAILED: $($service.Name) is unhealthy" -ForegroundColor Red
    }
}

# Test 6: Check Blockchain Monitoring Activity
Write-Host "`nTest 6: Checking Blockchain Monitoring Activity..." -ForegroundColor Yellow
try {
    $logs = docker logs qpesapay-blockchain-listener --tail 10 2>&1
    $recentActivity = $logs | Where-Object { $_ -match "Processing.*blocks" } | Select-Object -Last 3
    
    if ($recentActivity) {
        Write-Host "SUCCESS: Blockchain monitoring is active!" -ForegroundColor Green
        foreach ($activity in $recentActivity) {
            $cleanActivity = $activity -replace '.*INFO qpesapay_blockchain_listener::', ''
            Write-Host "  $cleanActivity" -ForegroundColor Gray
        }
    } else {
        Write-Host "WARNING: No recent blockchain monitoring activity found" -ForegroundColor Yellow
    }
} catch {
    Write-Host "WARNING: Could not check blockchain listener logs" -ForegroundColor Yellow
}

Write-Host "`nTEST SUMMARY" -ForegroundColor Green
Write-Host "============" -ForegroundColor Green
Write-Host "Multi-currency wallets created and ready for monitoring" -ForegroundColor White
Write-Host "Blockchain listener is actively monitoring all networks" -ForegroundColor White
Write-Host "All services are healthy and communicating" -ForegroundColor White

Write-Host "`nWallet Addresses Created:" -ForegroundColor Yellow
if ($btcWallet) { Write-Host "Bitcoin: $($btcWallet.address)" -ForegroundColor White }
if ($ethWallet) { Write-Host "Ethereum: $($ethWallet.address)" -ForegroundColor White }
if ($tronWallet) { Write-Host "TRON: $($tronWallet.address)" -ForegroundColor White }
