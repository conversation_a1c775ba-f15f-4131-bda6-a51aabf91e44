use std::collections::HashMap;
use chrono::{DateTime, Utc};
use hmac::{Hmac, Mac};
use sha2::Sha256;
use serde::{Deserialize, Serialize};
use tracing::info;

use crate::config::Config;
use crate::error::{AppError, Result};

type HmacSha256 = Hmac<Sha256>;

#[derive(Debug, <PERSON>lone)]
pub struct YellowcardService {
    client: reqwest::Client,
    api_key: String,
    api_secret: String,
    base_url: String,
    webhook_secret: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct YellowcardSellRequest {
    pub amount: String,
    pub currency: String, // "BTC", "USDT"
    pub recipient_phone: String,
    pub recipient_name: String,
    pub reference: String,
    pub callback_url: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct YellowcardSellResponse {
    pub transaction_id: String,
    pub status: String,
    pub amount_crypto: String,
    pub amount_kes: String,
    pub exchange_rate: String,
    pub fee: String,
    pub recipient_phone: String,
    pub created_at: DateTime<Utc>,
    pub estimated_completion: Option<DateTime<Utc>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct YellowcardRatesResponse {
    pub rates: HashMap<String, YellowcardRate>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct YellowcardRate {
    pub currency: String,
    pub buy_rate: String,
    pub sell_rate: String,
    pub last_updated: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct YellowcardWebhookPayload {
    pub transaction_id: String,
    pub status: String,
    pub amount_crypto: String,
    pub amount_kes: String,
    pub reference: String,
    pub completed_at: Option<DateTime<Utc>>,
    pub failure_reason: Option<String>,
}

impl YellowcardService {
    pub fn new(config: &Config) -> Self {
        Self {
            client: reqwest::Client::new(),
            api_key: config.yellowcard_api_key.clone(),
            api_secret: config.yellowcard_api_secret.clone(),
            base_url: config.yellowcard_base_url.clone(),
            webhook_secret: config.yellowcard_webhook_secret.clone(),
        }
    }

    pub async fn get_exchange_rates(&self) -> Result<YellowcardRatesResponse> {
        let url = format!("{}/api/v1/rates", self.base_url);
        
        let response = self.client
            .get(&url)
            .header("Authorization", format!("Bearer {}", self.api_key))
            .header("Content-Type", "application/json")
            .send()
            .await?;

        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_default();
            return Err(AppError::ExternalApi(format!("Failed to get rates: {}", error_text)));
        }

        let rates = response.json::<YellowcardRatesResponse>().await?;
        Ok(rates)
    }

    pub async fn sell_crypto(&self, request: YellowcardSellRequest) -> Result<YellowcardSellResponse> {
        let url = format!("{}/api/v1/sell", self.base_url);
        let timestamp = Utc::now().timestamp();
        
        // Create request body
        let body = serde_json::to_string(&request)?;
        
        // Generate signature
        let signature = self.generate_signature("POST", "/api/v1/sell", &body, timestamp)?;

        let response = self.client
            .post(&url)
            .header("Authorization", format!("Bearer {}", self.api_key))
            .header("X-Timestamp", timestamp.to_string())
            .header("X-Signature", signature)
            .header("Content-Type", "application/json")
            .body(body)
            .send()
            .await?;

        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_default();
            return Err(AppError::ExternalApi(format!("Failed to sell crypto: {}", error_text)));
        }

        let sell_response = response.json::<YellowcardSellResponse>().await?;
        info!("Yellowcard sell transaction created: {}", sell_response.transaction_id);
        
        Ok(sell_response)
    }

    pub async fn get_transaction_status(&self, transaction_id: &str) -> Result<YellowcardSellResponse> {
        let url = format!("{}/api/v1/transactions/{}", self.base_url, transaction_id);
        let timestamp = Utc::now().timestamp();
        
        // Generate signature for GET request
        let signature = self.generate_signature("GET", &format!("/api/v1/transactions/{}", transaction_id), "", timestamp)?;

        let response = self.client
            .get(&url)
            .header("Authorization", format!("Bearer {}", self.api_key))
            .header("X-Timestamp", timestamp.to_string())
            .header("X-Signature", signature)
            .send()
            .await?;

        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_default();
            return Err(AppError::ExternalApi(format!("Failed to get transaction status: {}", error_text)));
        }

        let transaction = response.json::<YellowcardSellResponse>().await?;
        Ok(transaction)
    }

    pub async fn handle_webhook(&self, payload: serde_json::Value) -> Result<()> {
        // Verify webhook signature (implementation depends on Yellowcard's webhook format)
        // For now, we'll just log the webhook
        info!("Received Yellowcard webhook: {:?}", payload);

        // Parse webhook payload
        let webhook: YellowcardWebhookPayload = serde_json::from_value(payload)
            .map_err(|e| AppError::Parse(format!("Invalid webhook payload: {}", e)))?;

        // TODO: Update settlement status in database based on webhook
        info!("Processing webhook for transaction: {}", webhook.transaction_id);

        Ok(())
    }

    fn generate_signature(&self, method: &str, path: &str, body: &str, timestamp: i64) -> Result<String> {
        // Create the string to sign: METHOD + PATH + TIMESTAMP + BODY
        let string_to_sign = format!("{}{}{}{}", method, path, timestamp, body);
        
        // Create HMAC-SHA256 signature
        let mut mac = HmacSha256::new_from_slice(self.api_secret.as_bytes())
            .map_err(|e| AppError::Internal(format!("Invalid secret key: {}", e)))?;
        
        mac.update(string_to_sign.as_bytes());
        let signature = mac.finalize().into_bytes();
        
        // Return hex-encoded signature
        Ok(hex::encode(signature))
    }

    pub fn verify_webhook_signature(&self, payload: &str, signature: &str) -> Result<bool> {
        let mut mac = HmacSha256::new_from_slice(self.webhook_secret.as_bytes())
            .map_err(|e| AppError::Internal(format!("Invalid webhook secret: {}", e)))?;
        
        mac.update(payload.as_bytes());
        let expected_signature = mac.finalize().into_bytes();
        let expected_hex = hex::encode(expected_signature);
        
        Ok(signature == expected_hex)
    }

    pub async fn validate_phone_number(&self, phone: &str) -> Result<bool> {
        // Basic validation for Kenyan phone numbers
        // Should start with +254 or 254 or 07/01
        let cleaned = phone.replace("+", "").replace(" ", "").replace("-", "");
        
        if cleaned.starts_with("254") && cleaned.len() == 12 {
            return Ok(true);
        }
        
        if (cleaned.starts_with("07") || cleaned.starts_with("01")) && cleaned.len() == 10 {
            return Ok(true);
        }
        
        Ok(false)
    }

    pub fn format_kenyan_phone(&self, phone: &str) -> String {
        let cleaned = phone.replace("+", "").replace(" ", "").replace("-", "");
        
        if cleaned.starts_with("254") {
            format!("+{}", cleaned)
        } else if cleaned.starts_with("07") || cleaned.starts_with("01") {
            format!("+254{}", &cleaned[1..])
        } else {
            phone.to_string()
        }
    }
}
