use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::Json,
    routing::{get, post},
    Router,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tower_http::cors::Cors<PERSON>ayer;
use tracing::{info, warn};
use uuid::Uuid;

mod config;
mod database;
mod error;
mod models;
mod services;

use config::Config;
use database::Database;
use error::{AppError, Result};
use models::*;
use services::{WalletService, CryptoService};

#[derive(Clone)]
pub struct AppState {
    pub db: Database,
    pub wallet_service: Arc<WalletService>,
    pub crypto_service: Arc<CryptoService>,
    pub config: Config,
}

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize tracing
    tracing_subscriber::fmt::init();

    // Load configuration
    let config = Config::from_env()?;
    info!("Starting Qpesapay Wallet Service on port {}", config.port);

    // Initialize database
    let db = Database::new(&config.database_url).await?;
    db.migrate().await?;

    // Initialize services
    let crypto_service = Arc::new(CryptoService::new(&config.encryption_key)?);
    let wallet_service = Arc::new(WalletService::new(db.clone(), crypto_service.clone()));

    // Create application state
    let state = AppState {
        db,
        wallet_service,
        crypto_service,
        config,
    };

    // Build router
    let app = Router::new()
        .route("/health", get(health_check))
        .route("/wallets", post(create_wallet))
        .route("/wallets/:user_id", get(get_user_wallets))
        .route("/wallets/:wallet_id/balance", get(get_wallet_balance))
        .route("/wallets/:wallet_id/addresses", post(generate_address))
        .route("/wallets/:wallet_id/addresses", get(get_wallet_addresses))
        .route("/wallets/:wallet_id/transactions", get(get_wallet_transactions))
        .route("/wallets/:wallet_id/send", post(send_transaction))
        .layer(CorsLayer::permissive())
        .with_state(state.clone());

    // Start server
    let listener = tokio::net::TcpListener::bind(format!("0.0.0.0:{}", state.config.port))
        .await
        .unwrap();

    info!("Wallet service listening on port {}", state.config.port);
    axum::serve(listener, app).await.unwrap();

    Ok(())
}

// Health check endpoint
async fn health_check() -> Json<serde_json::Value> {
    Json(serde_json::json!({
        "status": "healthy",
        "service": "qpesapay-wallet-service",
        "timestamp": chrono::Utc::now()
    }))
}

// Create a new wallet
async fn create_wallet(
    State(state): State<AppState>,
    Json(request): Json<CreateWalletRequest>,
) -> Result<Json<WalletResponse>> {
    let wallet = state.wallet_service.create_wallet(request).await?;
    Ok(Json(wallet))
}

// Get all wallets for a user
async fn get_user_wallets(
    State(state): State<AppState>,
    Path(user_id): Path<Uuid>,
) -> Result<Json<Vec<WalletResponse>>> {
    let wallets = state.wallet_service.get_user_wallets(user_id).await?;
    Ok(Json(wallets))
}

// Get wallet balance
async fn get_wallet_balance(
    State(state): State<AppState>,
    Path(wallet_id): Path<Uuid>,
) -> Result<Json<BalanceResponse>> {
    let balance = state.wallet_service.get_wallet_balance(wallet_id).await?;
    Ok(Json(balance))
}

// Generate new address for wallet
async fn generate_address(
    State(state): State<AppState>,
    Path(wallet_id): Path<Uuid>,
) -> Result<Json<AddressResponse>> {
    let address = state.wallet_service.generate_new_address(wallet_id).await?;
    Ok(Json(address))
}

// Get all addresses for a wallet
async fn get_wallet_addresses(
    State(state): State<AppState>,
    Path(wallet_id): Path<Uuid>,
    Query(params): Query<AddressQueryParams>,
) -> Result<Json<Vec<AddressResponse>>> {
    let addresses = state.wallet_service.get_wallet_addresses(wallet_id, params.limit, params.offset).await?;
    Ok(Json(addresses))
}

// Get wallet transactions
async fn get_wallet_transactions(
    State(state): State<AppState>,
    Path(wallet_id): Path<Uuid>,
    Query(params): Query<TransactionQueryParams>,
) -> Result<Json<Vec<TransactionResponse>>> {
    let transactions = state.wallet_service.get_wallet_transactions(wallet_id, params.limit, params.offset).await?;
    Ok(Json(transactions))
}

// Send transaction
async fn send_transaction(
    State(state): State<AppState>,
    Path(wallet_id): Path<Uuid>,
    Json(request): Json<SendTransactionRequest>,
) -> Result<Json<TransactionResponse>> {
    let transaction = state.wallet_service.send_transaction(wallet_id, request).await?;
    Ok(Json(transaction))
}

#[derive(Deserialize)]
struct AddressQueryParams {
    limit: Option<i64>,
    offset: Option<i64>,
}

#[derive(Deserialize)]
struct TransactionQueryParams {
    limit: Option<i64>,
    offset: Option<i64>,
}
