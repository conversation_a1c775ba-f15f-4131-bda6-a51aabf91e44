// K6 Load Testing Script for Qpesapay Services
import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate } from 'k6/metrics';

// Custom metrics
const authSuccessRate = new Rate('auth_success_rate');
const walletCreationRate = new Rate('wallet_creation_rate');

// Test configuration
export const options = {
  stages: [
    { duration: '2m', target: 10 }, // Ramp up to 10 users
    { duration: '5m', target: 10 }, // Stay at 10 users
    { duration: '2m', target: 50 }, // Ramp up to 50 users
    { duration: '5m', target: 50 }, // Stay at 50 users
    { duration: '2m', target: 0 },  // Ramp down to 0 users
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95% of requests must complete below 500ms
    auth_success_rate: ['rate>0.95'], // 95% of auth requests should succeed
    wallet_creation_rate: ['rate>0.90'], // 90% of wallet creations should succeed
  },
};

const BASE_URL = __ENV.BASE_URL || 'http://localhost:5001';
const WALLET_SERVICE_URL = __ENV.WALLET_SERVICE_URL || 'http://localhost:8080';

// Test data
const testUsers = [
  { email: '<EMAIL>', password: 'LoadTest123!', phone: '+254712345001' },
  { email: '<EMAIL>', password: 'LoadTest123!', phone: '+254712345002' },
  { email: '<EMAIL>', password: 'LoadTest123!', phone: '+254712345003' },
];

export function setup() {
  console.log('Setting up load test...');
  
  // Create test users
  testUsers.forEach((user, index) => {
    const signupPayload = {
      email: user.email,
      password: user.password,
      phoneNumber: user.phone,
      role: 'personal'
    };

    const signupResponse = http.post(`${BASE_URL}/api/auth/signup`, JSON.stringify(signupPayload), {
      headers: { 'Content-Type': 'application/json' },
    });

    if (signupResponse.status === 201 || signupResponse.status === 409) {
      console.log(`Test user ${index + 1} ready`);
    }
  });

  return { users: testUsers };
}

export default function (data) {
  const user = data.users[Math.floor(Math.random() * data.users.length)];
  
  // Test authentication flow
  testAuthFlow(user);
  
  sleep(1);
  
  // Test wallet operations (if authenticated)
  testWalletOperations(user);
  
  sleep(1);
}

function testAuthFlow(user) {
  const loginPayload = {
    email: user.email,
    password: user.password
  };

  const loginResponse = http.post(`${BASE_URL}/api/auth/login`, JSON.stringify(loginPayload), {
    headers: { 'Content-Type': 'application/json' },
  });

  const loginSuccess = check(loginResponse, {
    'login status is 200 or 401': (r) => r.status === 200 || r.status === 401,
    'login response time < 200ms': (r) => r.timings.duration < 200,
  });

  authSuccessRate.add(loginSuccess && loginResponse.status === 200);

  if (loginResponse.status === 200) {
    const loginData = JSON.parse(loginResponse.body);
    user.token = loginData.token;
    user.userId = loginData.user.id;
  }
}

function testWalletOperations(user) {
  if (!user.token) return;

  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${user.token}`
  };

  // Test get user wallets
  const walletsResponse = http.get(`${WALLET_SERVICE_URL}/wallets/${user.userId}`, { headers });
  
  check(walletsResponse, {
    'get wallets status is 200': (r) => r.status === 200,
    'get wallets response time < 300ms': (r) => r.timings.duration < 300,
  });

  // Test wallet creation (occasionally)
  if (Math.random() < 0.1) { // 10% chance
    const currencies = ['BTC', 'USDT_ERC20', 'USDT_TRC20'];
    const currency = currencies[Math.floor(Math.random() * currencies.length)];
    
    const createWalletPayload = {
      user_id: user.userId,
      currency: currency,
      wallet_type: 'personal'
    };

    const createResponse = http.post(`${WALLET_SERVICE_URL}/wallets`, JSON.stringify(createWalletPayload), { headers });
    
    const walletCreationSuccess = check(createResponse, {
      'create wallet status is 200 or 409': (r) => r.status === 200 || r.status === 409,
      'create wallet response time < 500ms': (r) => r.timings.duration < 500,
    });

    walletCreationRate.add(walletCreationSuccess && createResponse.status === 200);
  }
}

export function teardown(data) {
  console.log('Load test completed');
  
  // Cleanup could be added here if needed
  // For now, we'll leave test data for analysis
}
