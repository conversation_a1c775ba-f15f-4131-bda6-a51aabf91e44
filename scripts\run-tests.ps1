# Qpesapay Test Runner Script
# This script runs all tests for the Qpesapay ecosystem

param(
    [string]$TestType = "all",  # all, unit, integration, rust, dotnet
    [switch]$Coverage = $false,
    [switch]$Verbose = $false
)

Write-Host "🧪 Qpesapay Test Suite Runner" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan

$ErrorActionPreference = "Stop"
$startTime = Get-Date

# Test results tracking
$testResults = @{
    "AuthService.Unit" = $false
    "AuthService.Integration" = $false
    "KycService.Unit" = $false
    "KycService.Integration" = $false
    "WalletService.Rust" = $false
    "BlockchainListener.Rust" = $false
    "SettlementService.Rust" = $false
}

function Write-TestHeader($serviceName) {
    Write-Host ""
    Write-Host "🔍 Testing: $serviceName" -ForegroundColor Yellow
    Write-Host "----------------------------------------" -ForegroundColor Yellow
}

function Write-TestResult($serviceName, $success) {
    if ($success) {
        Write-Host "✅ $serviceName - PASSED" -ForegroundColor Green
    } else {
        Write-Host "❌ $serviceName - FAILED" -ForegroundColor Red
    }
}

# .NET Tests
function Run-DotNetTests {
    if ($TestType -eq "all" -or $TestType -eq "dotnet" -or $TestType -eq "unit") {
        Write-TestHeader "Auth Service Unit Tests"
        try {
            $testArgs = @("test", "Qpesapay.AuthService.Tests/Qpesapay.AuthService.Tests.csproj")
            if ($Coverage) { $testArgs += "--collect:XPlat Code Coverage" }
            if ($Verbose) { $testArgs += "--verbosity", "detailed" }
            
            & dotnet @testArgs
            $testResults["AuthService.Unit"] = $LASTEXITCODE -eq 0
            Write-TestResult "Auth Service Unit Tests" $testResults["AuthService.Unit"]
        }
        catch {
            Write-Host "❌ Auth Service Unit Tests - ERROR: $_" -ForegroundColor Red
            $testResults["AuthService.Unit"] = $false
        }
    }

    if ($TestType -eq "all" -or $TestType -eq "dotnet" -or $TestType -eq "integration") {
        Write-TestHeader "Auth Service Integration Tests"
        try {
            # Ensure Docker is running for testcontainers
            docker info > $null 2>&1
            if ($LASTEXITCODE -ne 0) {
                throw "Docker is not running. Integration tests require Docker for testcontainers."
            }

            $testArgs = @("test", "Qpesapay.AuthService.Tests/Qpesapay.AuthService.Tests.csproj", "--filter", "Category=Integration")
            if ($Coverage) { $testArgs += "--collect:XPlat Code Coverage" }
            if ($Verbose) { $testArgs += "--verbosity", "detailed" }
            
            & dotnet @testArgs
            $testResults["AuthService.Integration"] = $LASTEXITCODE -eq 0
            Write-TestResult "Auth Service Integration Tests" $testResults["AuthService.Integration"]
        }
        catch {
            Write-Host "❌ Auth Service Integration Tests - ERROR: $_" -ForegroundColor Red
            $testResults["AuthService.Integration"] = $false
        }
    }

    # KYC Service Tests (if they exist)
    if (Test-Path "Qpesapay.KycService.Tests") {
        if ($TestType -eq "all" -or $TestType -eq "dotnet" -or $TestType -eq "unit") {
            Write-TestHeader "KYC Service Unit Tests"
            try {
                $testArgs = @("test", "Qpesapay.KycService.Tests/Qpesapay.KycService.Tests.csproj")
                if ($Coverage) { $testArgs += "--collect:XPlat Code Coverage" }
                if ($Verbose) { $testArgs += "--verbosity", "detailed" }
                
                & dotnet @testArgs
                $testResults["KycService.Unit"] = $LASTEXITCODE -eq 0
                Write-TestResult "KYC Service Unit Tests" $testResults["KycService.Unit"]
            }
            catch {
                Write-Host "❌ KYC Service Unit Tests - ERROR: $_" -ForegroundColor Red
                $testResults["KycService.Unit"] = $false
            }
        }
    }
}

# Rust Tests
function Run-RustTests {
    if ($TestType -eq "all" -or $TestType -eq "rust") {
        Write-TestHeader "Wallet Service Tests (Rust)"
        try {
            Set-Location "wallet-service"
            $env:RUST_LOG = "debug"
            $env:DATABASE_URL = "postgresql://test_user:test_password@localhost:5432/test_db"
            
            if ($Verbose) {
                cargo test -- --nocapture
            } else {
                cargo test
            }
            
            $testResults["WalletService.Rust"] = $LASTEXITCODE -eq 0
            Write-TestResult "Wallet Service Tests" $testResults["WalletService.Rust"]
            Set-Location ".."
        }
        catch {
            Write-Host "❌ Wallet Service Tests - ERROR: $_" -ForegroundColor Red
            $testResults["WalletService.Rust"] = $false
            Set-Location ".."
        }

        Write-TestHeader "Blockchain Listener Tests (Rust)"
        try {
            Set-Location "blockchain-listener"
            $env:RUST_LOG = "debug"
            $env:DATABASE_URL = "postgresql://test_user:test_password@localhost:5432/test_db"
            
            if ($Verbose) {
                cargo test -- --nocapture
            } else {
                cargo test
            }
            
            $testResults["BlockchainListener.Rust"] = $LASTEXITCODE -eq 0
            Write-TestResult "Blockchain Listener Tests" $testResults["BlockchainListener.Rust"]
            Set-Location ".."
        }
        catch {
            Write-Host "❌ Blockchain Listener Tests - ERROR: $_" -ForegroundColor Red
            $testResults["BlockchainListener.Rust"] = $false
            Set-Location ".."
        }

        Write-TestHeader "Settlement Service Tests (Rust)"
        try {
            Set-Location "settlement-service"
            $env:RUST_LOG = "debug"
            $env:DATABASE_URL = "postgresql://test_user:test_password@localhost:5432/test_db"
            
            if ($Verbose) {
                cargo test -- --nocapture
            } else {
                cargo test
            }
            
            $testResults["SettlementService.Rust"] = $LASTEXITCODE -eq 0
            Write-TestResult "Settlement Service Tests" $testResults["SettlementService.Rust"]
            Set-Location ".."
        }
        catch {
            Write-Host "❌ Settlement Service Tests - ERROR: $_" -ForegroundColor Red
            $testResults["SettlementService.Rust"] = $false
            Set-Location ".."
        }
    }
}

# Main execution
try {
    Write-Host "Test Type: $TestType" -ForegroundColor Cyan
    Write-Host "Coverage: $Coverage" -ForegroundColor Cyan
    Write-Host "Verbose: $Verbose" -ForegroundColor Cyan
    Write-Host ""

    # Check prerequisites
    Write-Host "🔧 Checking prerequisites..." -ForegroundColor Cyan
    
    # Check .NET
    try {
        $dotnetVersion = dotnet --version
        Write-Host "✅ .NET SDK: $dotnetVersion" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ .NET SDK not found" -ForegroundColor Red
        exit 1
    }

    # Check Rust
    try {
        $rustVersion = cargo --version
        Write-Host "✅ Rust: $rustVersion" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Rust/Cargo not found" -ForegroundColor Red
        exit 1
    }

    # Run tests based on type
    if ($TestType -eq "all" -or $TestType -eq "dotnet" -or $TestType -eq "unit" -or $TestType -eq "integration") {
        Run-DotNetTests
    }

    if ($TestType -eq "all" -or $TestType -eq "rust") {
        Run-RustTests
    }

    # Summary
    Write-Host ""
    Write-Host "📊 Test Results Summary" -ForegroundColor Cyan
    Write-Host "========================" -ForegroundColor Cyan
    
    $totalTests = 0
    $passedTests = 0
    
    foreach ($test in $testResults.GetEnumerator()) {
        if ($test.Value -ne $null) {
            $totalTests++
            if ($test.Value) {
                $passedTests++
                Write-Host "✅ $($test.Key)" -ForegroundColor Green
            } else {
                Write-Host "❌ $($test.Key)" -ForegroundColor Red
            }
        }
    }

    $endTime = Get-Date
    $duration = $endTime - $startTime

    Write-Host ""
    Write-Host "📈 Results: $passedTests/$totalTests tests passed" -ForegroundColor Cyan
    Write-Host "⏱️  Duration: $($duration.ToString('mm\:ss'))" -ForegroundColor Cyan

    if ($passedTests -eq $totalTests) {
        Write-Host "🎉 All tests passed!" -ForegroundColor Green
        exit 0
    } else {
        Write-Host "💥 Some tests failed!" -ForegroundColor Red
        exit 1
    }
}
catch {
    Write-Host "💥 Test runner failed: $_" -ForegroundColor Red
    exit 1
}
