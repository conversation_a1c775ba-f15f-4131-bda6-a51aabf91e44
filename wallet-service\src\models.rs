use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Wallet {
    pub id: Uuid,
    pub user_id: Uuid,
    pub currency: String,
    pub address: String,
    pub private_key_encrypted: String,
    pub public_key: Option<String>,
    pub derivation_path: Option<String>,
    pub balance: rust_decimal::Decimal,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct WalletAddress {
    pub id: Uuid,
    pub wallet_id: Uuid,
    pub address: String,
    pub derivation_index: i32,
    pub is_used: bool,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Transaction {
    pub id: Uuid,
    pub user_id: Option<Uuid>,
    pub wallet_id: Option<Uuid>,
    pub transaction_hash: Option<String>,
    pub transaction_type: String,
    pub currency: String,
    pub amount: rust_decimal::Decimal,
    pub fee: rust_decimal::Decimal,
    pub from_address: Option<String>,
    pub to_address: Option<String>,
    pub status: String,
    pub confirmations: i32,
    pub block_number: Option<i64>,
    pub network: Option<String>,
    pub created_at: DateTime<Utc>,
    pub confirmed_at: Option<DateTime<Utc>>,
}

// Request/Response DTOs
#[derive(Debug, Deserialize)]
pub struct CreateWalletRequest {
    pub user_id: Uuid,
    pub currency: String,
    pub wallet_type: Option<String>, // "personal" or "merchant"
}

#[derive(Debug, Serialize)]
pub struct WalletResponse {
    pub id: Uuid,
    pub user_id: Uuid,
    pub currency: String,
    pub address: String,
    pub balance: String,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Serialize)]
pub struct AddressResponse {
    pub id: Uuid,
    pub wallet_id: Uuid,
    pub address: String,
    pub derivation_index: i32,
    pub is_used: bool,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Serialize)]
pub struct BalanceResponse {
    pub wallet_id: Uuid,
    pub currency: String,
    pub balance: String,
    pub confirmed_balance: String,
    pub unconfirmed_balance: String,
    pub last_updated: DateTime<Utc>,
}

#[derive(Debug, Serialize)]
pub struct TransactionResponse {
    pub id: Uuid,
    pub transaction_hash: Option<String>,
    pub transaction_type: String,
    pub currency: String,
    pub amount: String,
    pub fee: String,
    pub from_address: Option<String>,
    pub to_address: Option<String>,
    pub status: String,
    pub confirmations: i32,
    pub block_number: Option<i64>,
    pub network: Option<String>,
    pub created_at: DateTime<Utc>,
    pub confirmed_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Deserialize)]
pub struct SendTransactionRequest {
    pub to_address: String,
    pub amount: String,
    pub fee_rate: Option<String>,
    pub memo: Option<String>,
}

#[derive(Debug, Clone)]
pub enum Currency {
    Bitcoin,
    UsdtErc20,
    UsdtTrc20,
}

impl Currency {
    pub fn from_string(s: &str) -> Option<Self> {
        match s.to_uppercase().as_str() {
            "BTC" => Some(Currency::Bitcoin),
            "USDT_ERC20" => Some(Currency::UsdtErc20),
            "USDT_TRC20" => Some(Currency::UsdtTrc20),
            _ => None,
        }
    }

    pub fn to_string(&self) -> &'static str {
        match self {
            Currency::Bitcoin => "BTC",
            Currency::UsdtErc20 => "USDT_ERC20",
            Currency::UsdtTrc20 => "USDT_TRC20",
        }
    }
}

#[derive(Debug, Clone)]
pub struct HDWallet {
    pub mnemonic: String,
    pub seed: Vec<u8>,
    pub master_key: String,
}

#[derive(Debug, Clone)]
pub struct KeyPair {
    pub private_key: String,
    pub public_key: String,
    pub address: String,
}

// Blockchain-specific models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BitcoinTransaction {
    pub txid: String,
    pub inputs: Vec<BitcoinInput>,
    pub outputs: Vec<BitcoinOutput>,
    pub fee: u64,
    pub size: u32,
    pub confirmations: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BitcoinInput {
    pub txid: String,
    pub vout: u32,
    pub value: u64,
    pub address: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BitcoinOutput {
    pub value: u64,
    pub address: String,
    pub script_pubkey: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EthereumTransaction {
    pub hash: String,
    pub from: String,
    pub to: String,
    pub value: String,
    pub gas: String,
    pub gas_price: String,
    pub nonce: u64,
    pub block_number: Option<u64>,
    pub confirmations: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TronTransaction {
    pub txid: String,
    pub from: String,
    pub to: String,
    pub amount: u64,
    pub fee: u64,
    pub block_number: u64,
    pub confirmations: u32,
}
