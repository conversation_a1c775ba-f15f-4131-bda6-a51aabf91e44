use std::collections::HashMap;
use chrono::{Duration, Utc};
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use tracing::{info, warn, error};
use uuid::Uuid;

use crate::config::Config;
use crate::error::{AppError, Result};
use crate::models::ExchangeRate;

#[derive(Debug, <PERSON>lone)]
pub struct ExchangeRateService {
    client: reqwest::Client,
    api_key: String,
    base_url: String,
    cache: std::sync::Arc<tokio::sync::RwLock<HashMap<String, ExchangeRate>>>,
}

#[derive(Debug, Serialize, Deserialize)]
struct CoinGeckoResponse {
    bitcoin: CoinGeckoPrice,
    tether: CoinGeckoPrice,
}

#[derive(Debug, Serialize, Deserialize)]
struct CoinGeckoPrice {
    kes: f64,
}

#[derive(Debug, Serialize, Deserialize)]
struct YellowCardRateResponse {
    rates: Vec<YellowCardRate>,
}

#[derive(Debug, Serialize, Deserialize)]
struct YellowCardRate {
    from_currency: String,
    to_currency: String,
    rate: String,
    timestamp: String,
}

impl ExchangeRateService {
    pub fn new(config: &Config) -> Self {
        Self {
            client: reqwest::Client::new(),
            api_key: config.exchange_rate_api_key.clone(),
            base_url: "https://api.coingecko.com/api/v3".to_string(),
            cache: std::sync::Arc::new(tokio::sync::RwLock::new(HashMap::new())),
        }
    }

    pub async fn get_current_rate(&self, from_currency: &str, to_currency: &str) -> Result<ExchangeRate> {
        let cache_key = format!("{}_{}", from_currency, to_currency);
        
        // Check cache first
        {
            let cache = self.cache.read().await;
            if let Some(cached_rate) = cache.get(&cache_key) {
                if cached_rate.expires_at > Utc::now() {
                    info!("Using cached exchange rate: {} -> {}", from_currency, to_currency);
                    return Ok(cached_rate.clone());
                }
            }
        }

        // Fetch fresh rate
        let rate = self.fetch_exchange_rate(from_currency, to_currency).await?;
        
        // Update cache
        {
            let mut cache = self.cache.write().await;
            cache.insert(cache_key, rate.clone());
        }

        Ok(rate)
    }

    pub async fn update_all_rates(&self) -> Result<()> {
        info!("Updating all exchange rates");

        let currencies = vec![
            ("BTC", "KES"),
            ("USDT", "KES"),
        ];

        for (from, to) in currencies {
            match self.fetch_exchange_rate(from, to).await {
                Ok(rate) => {
                    let cache_key = format!("{}_{}", from, to);
                    let mut cache = self.cache.write().await;
                    info!("Updated rate: {} -> {} = {}", from, to, rate.rate);
                    cache.insert(cache_key, rate);
                }
                Err(e) => {
                    error!("Failed to update rate {} -> {}: {}", from, to, e);
                }
            }
        }

        Ok(())
    }

    async fn fetch_exchange_rate(&self, from_currency: &str, to_currency: &str) -> Result<ExchangeRate> {
        info!("Fetching exchange rate: {} -> {}", from_currency, to_currency);

        // For development, use mock rates
        if self.api_key == "mock-exchange-rate-key" {
            return self.get_mock_rate(from_currency, to_currency);
        }

        // Try CoinGecko API first
        match self.fetch_from_coingecko(from_currency, to_currency).await {
            Ok(rate) => Ok(rate),
            Err(e) => {
                warn!("CoinGecko API failed: {}, using mock rate", e);
                self.get_mock_rate(from_currency, to_currency)
            }
        }
    }

    async fn fetch_from_coingecko(&self, from_currency: &str, to_currency: &str) -> Result<ExchangeRate> {
        let coin_id = match from_currency {
            "BTC" => "bitcoin",
            "USDT" => "tether",
            _ => return Err(AppError::UnsupportedCurrency(format!("Unsupported currency: {}", from_currency))),
        };

        let vs_currency = match to_currency {
            "KES" => "kes",
            _ => return Err(AppError::UnsupportedCurrency(format!("Unsupported target currency: {}", to_currency))),
        };

        let url = format!(
            "{}/simple/price?ids={}&vs_currencies={}",
            self.base_url, coin_id, vs_currency
        );

        let response = self
            .client
            .get(&url)
            .header("User-Agent", "Qpesapay/1.0")
            .send()
            .await
            .map_err(AppError::Http)?;

        if !response.status().is_success() {
            return Err(AppError::ExternalApi(format!(
                "CoinGecko API error: {}",
                response.status()
            )));
        }

        let data: serde_json::Value = response.json().await.map_err(AppError::Http)?;
        
        let price = data
            .get(coin_id)
            .and_then(|coin| coin.get(vs_currency))
            .and_then(|price| price.as_f64())
            .ok_or_else(|| AppError::Parse("Invalid CoinGecko response format".to_string()))?;

        let rate = Decimal::try_from(price)
            .map_err(|e| AppError::Parse(format!("Invalid price value: {}", e)))?;

        Ok(ExchangeRate {
            id: Uuid::new_v4(),
            from_currency: from_currency.to_string(),
            to_currency: to_currency.to_string(),
            rate,
            source: "coingecko".to_string(),
            created_at: Utc::now(),
            expires_at: Utc::now() + Duration::minutes(5),
        })
    }

    fn get_mock_rate(&self, from_currency: &str, to_currency: &str) -> Result<ExchangeRate> {
        let rate = match (from_currency, to_currency) {
            ("BTC", "KES") => Decimal::new(6500000, 2), // 65,000 KES per BTC
            ("USDT", "KES") => Decimal::new(15000, 2),   // 150 KES per USDT
            _ => return Err(AppError::UnsupportedCurrency(format!(
                "Unsupported currency pair: {} -> {}",
                from_currency, to_currency
            ))),
        };

        info!("Using mock exchange rate: {} -> {} = {}", from_currency, to_currency, rate);

        Ok(ExchangeRate {
            id: Uuid::new_v4(),
            from_currency: from_currency.to_string(),
            to_currency: to_currency.to_string(),
            rate,
            source: "mock".to_string(),
            created_at: Utc::now(),
            expires_at: Utc::now() + Duration::minutes(5),
        })
    }

    pub async fn get_conversion_preview(
        &self,
        from_currency: &str,
        to_currency: &str,
        amount: Decimal,
    ) -> Result<serde_json::Value> {
        let rate = self.get_current_rate(from_currency, to_currency).await?;
        let converted_amount = amount * rate.rate;

        // Calculate fees (mock)
        let exchange_fee = converted_amount * Decimal::new(25, 3); // 2.5%
        let processing_fee = Decimal::new(5000, 2); // 50 KES
        let total_fees = exchange_fee + processing_fee;
        let net_amount = converted_amount - total_fees;

        Ok(serde_json::json!({
            "from_currency": from_currency,
            "to_currency": to_currency,
            "input_amount": amount.to_string(),
            "exchange_rate": rate.rate.to_string(),
            "gross_amount": converted_amount.to_string(),
            "fees": {
                "exchange_fee": exchange_fee.to_string(),
                "processing_fee": processing_fee.to_string(),
                "total_fees": total_fees.to_string()
            },
            "net_amount": net_amount.to_string(),
            "rate_source": rate.source,
            "rate_timestamp": rate.created_at,
            "rate_expires_at": rate.expires_at
        }))
    }

    pub async fn get_supported_pairs(&self) -> Vec<(String, String)> {
        vec![
            ("BTC".to_string(), "KES".to_string()),
            ("USDT".to_string(), "KES".to_string()),
        ]
    }

    pub async fn clear_cache(&self) {
        let mut cache = self.cache.write().await;
        cache.clear();
        info!("Exchange rate cache cleared");
    }
}
