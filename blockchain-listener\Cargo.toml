[package]
name = "qpesapay-blockchain-listener"
version = "0.1.0"
edition = "2021"

[dependencies]
# Async runtime
tokio = { version = "1.0", features = ["full"] }
tokio-stream = "0.1"
futures = "0.3"

# Web framework for health checks and metrics
axum = "0.7"
tower = "0.4"
tower-http = { version = "0.5", features = ["cors", "trace"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Database
sqlx = { version = "0.8", features = ["runtime-tokio-rustls", "postgres", "uuid", "chrono", "json"] }
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }

# Blockchain clients (mock implementations for now)
# bitcoin = "0.30"
# bitcoincore-rpc = "0.17"
# web3 = "0.18"
# ethers = { version = "1.0", features = ["ws"] }

# HTTP client
reqwest = { version = "0.12", features = ["json"] }

# WebSocket (commented out for now)
# tokio-tungstenite = { version = "0.20", features = ["native-tls"] }
# tungstenite = "0.20"

# Logging and tracing
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# Configuration
config = "0.14"
dotenv = "0.15"

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Async traits
async-trait = "0.1"

# Serialization
hex = "0.4"
base58 = "0.2"

# Decimal arithmetic
rust_decimal = { version = "1.0", features = ["serde"] }

# Environment variables
envy = "0.4"

# Retry logic (commented out for now)
# backoff = { version = "0.4", features = ["tokio"] }

# Metrics (commented out for now)
# prometheus = "0.13"

# [dev-dependencies]
# Testing (commented out for now)
# tokio-test = "0.4"
# mockall = "0.13"
# tempfile = "3.0"
# testcontainers = "0.23"
# testcontainers-modules = { version = "0.11", features = ["postgres"] }
# serial_test = "3.0"
