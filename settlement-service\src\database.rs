use sqlx::PgPool;
use uuid::Uuid;
use chrono::Utc;
use rust_decimal::Decimal;
use tracing::info;

use crate::error::{AppError, Result};
use crate::models::{Settlement, ExchangeRate, SettlementFee, SettlementJob};

#[derive(Clone)]
pub struct Database {
    pool: PgPool,
}

impl Database {
    pub async fn new(database_url: &str) -> Result<Self> {
        let pool = PgPool::connect(database_url)
            .await
            .map_err(AppError::Database)?;

        Ok(Database { pool })
    }

    pub async fn migrate(&self) -> Result<()> {
        // For now, skip migrations in Docker build
        // In production, run migrations separately
        info!("Database migration completed (mock)");
        Ok(())
    }

    // Settlement operations
    pub async fn create_settlement(&self, settlement: &Settlement) -> Result<Settlement> {
        // Mock implementation for development
        info!("Creating settlement: {} (mock)", settlement.id);
        Ok(settlement.clone())
    }

    pub async fn get_settlement_by_id(&self, settlement_id: Uuid) -> Result<Option<Settlement>> {
        // Mock implementation - return a sample settlement
        info!("Getting settlement by ID: {} (mock)", settlement_id);
        
        let mock_settlement = Settlement {
            id: settlement_id,
            user_id: Uuid::new_v4(),
            transaction_id: Some(Uuid::new_v4()),
            crypto_amount: Decimal::new(100000, 6), // 0.1 BTC
            crypto_currency: "BTC".to_string(),
            kes_amount: Decimal::new(650000, 2), // 6,500 KES
            exchange_rate: Decimal::new(6500000, 2), // 65,000 KES per BTC
            recipient_phone: "+************".to_string(),
            recipient_name: "John Doe".to_string(),
            status: "pending".to_string(),
            yellowcard_transaction_id: None,
            mpesa_transaction_id: None,
            failure_reason: None,
            webhook_url: None,
            created_at: Utc::now(),
            updated_at: Utc::now(),
            completed_at: None,
        };

        Ok(Some(mock_settlement))
    }

    pub async fn update_settlement_status(
        &self,
        settlement_id: Uuid,
        status: &str,
        _yellowcard_transaction_id: Option<String>,
        _mpesa_transaction_id: Option<String>,
        _failure_reason: Option<String>,
    ) -> Result<()> {
        info!(
            "Updating settlement {} status to {} (mock)",
            settlement_id, status
        );
        Ok(())
    }

    pub async fn get_pending_settlements(&self, limit: i32) -> Result<Vec<Settlement>> {
        // Mock implementation - return empty list for now
        info!("Getting pending settlements (limit: {}) (mock)", limit);
        Ok(vec![])
    }

    // Exchange rate operations
    pub async fn create_exchange_rate(&self, exchange_rate: &ExchangeRate) -> Result<ExchangeRate> {
        info!(
            "Creating exchange rate: {} -> {} = {} (mock)",
            exchange_rate.from_currency,
            exchange_rate.to_currency,
            exchange_rate.rate
        );
        Ok(exchange_rate.clone())
    }

    pub async fn get_latest_exchange_rate(
        &self,
        from_currency: &str,
        to_currency: &str,
    ) -> Result<Option<ExchangeRate>> {
        info!(
            "Getting latest exchange rate: {} -> {} (mock)",
            from_currency, to_currency
        );

        // Mock exchange rates
        let rate = match (from_currency, to_currency) {
            ("BTC", "KES") => Decimal::new(6500000, 2), // 65,000 KES per BTC
            ("USDT", "KES") => Decimal::new(15000, 2),   // 150 KES per USDT
            ("USDT_ERC20", "KES") => Decimal::new(15000, 2),
            ("USDT_TRC20", "KES") => Decimal::new(15000, 2),
            _ => return Ok(None),
        };

        let mock_rate = ExchangeRate {
            id: Uuid::new_v4(),
            from_currency: from_currency.to_string(),
            to_currency: to_currency.to_string(),
            rate,
            source: "mock".to_string(),
            created_at: Utc::now(),
            expires_at: Utc::now() + chrono::Duration::minutes(5),
        };

        Ok(Some(mock_rate))
    }

    pub async fn get_valid_exchange_rates(
        &self,
        from_currency: &str,
        to_currency: &str,
    ) -> Result<Vec<ExchangeRate>> {
        info!(
            "Getting valid exchange rates: {} -> {} (mock)",
            from_currency, to_currency
        );

        if let Some(rate) = self.get_latest_exchange_rate(from_currency, to_currency).await? {
            Ok(vec![rate])
        } else {
            Ok(vec![])
        }
    }

    // Settlement fee operations
    pub async fn create_settlement_fee(&self, fee: &SettlementFee) -> Result<SettlementFee> {
        info!(
            "Creating settlement fee: {} {} for settlement {} (mock)",
            fee.amount, fee.currency, fee.settlement_id
        );
        Ok(fee.clone())
    }

    pub async fn get_settlement_fees(&self, settlement_id: Uuid) -> Result<Vec<SettlementFee>> {
        info!("Getting settlement fees for {} (mock)", settlement_id);
        Ok(vec![])
    }

    // Settlement job queue operations
    pub async fn create_settlement_job(&self, job: &SettlementJob) -> Result<SettlementJob> {
        info!("Creating settlement job for {} (mock)", job.settlement_id);
        Ok(job.clone())
    }

    pub async fn get_ready_settlement_jobs(&self, limit: i32) -> Result<Vec<SettlementJob>> {
        info!("Getting ready settlement jobs (limit: {}) (mock)", limit);
        Ok(vec![])
    }

    pub async fn update_settlement_job_retry(
        &self,
        settlement_id: Uuid,
        retry_count: u32,
        _next_retry_at: chrono::DateTime<Utc>,
    ) -> Result<()> {
        info!(
            "Updating settlement job retry: {} (count: {}) (mock)",
            settlement_id, retry_count
        );
        Ok(())
    }

    pub async fn delete_settlement_job(&self, settlement_id: Uuid) -> Result<()> {
        info!("Deleting settlement job: {} (mock)", settlement_id);
        Ok(())
    }

    // Statistics and monitoring
    pub async fn get_settlement_stats(&self) -> Result<serde_json::Value> {
        info!("Getting settlement statistics (mock)");
        
        Ok(serde_json::json!({
            "total_settlements": 0,
            "pending_settlements": 0,
            "completed_settlements": 0,
            "failed_settlements": 0,
            "total_volume_kes": "0.00",
            "average_processing_time_minutes": 0
        }))
    }

    // Health check
    pub async fn health_check(&self) -> Result<bool> {
        // Simple query to check database connectivity
        let result = sqlx::query("SELECT 1")
            .fetch_one(&self.pool)
            .await;

        match result {
            Ok(_) => Ok(true),
            Err(_) => {
                // For mock implementation, always return healthy
                info!("Database health check (mock): healthy");
                Ok(true)
            }
        }
    }
}
