
## Purpose

Build a comprehensive web-first dual-account payment ecosystem for Kenya that enables merchants to accept BTC and USDT payments with automatic KES settlement, and personal users to manage crypto wallets with M-Pesa integration. Focus on responsive web interfaces that work seamlessly on mobile browsers.

## Core Principles

1. **Web-First Architecture**: Responsive web applications that work on all devices
2. **Financial Security**: Bank-grade security with proper encryption and compliance
3. **Kenya Market Focus**: M-Pesa integration and local payment preferences
4. **Progressive Enhancement**: Start with core web functionality, mobile apps later
5. **Regulatory Compliance**: CBK, CMA guidelines and KYC/AML requirements

---

## Goal

Build a production-ready web-based crypto-fiat payment processor that serves both merchants and personal users in Kenya, with automatic BTC and USDT-to-KES conversion, M-Pesa settlement, and comprehensive compliance features.

## Why

- **Market Opportunity**: Bridge crypto adoption gap in Kenya's mobile-first economy
- **Merchant Value**: Enable businesses to accept crypto with fiat settlement
- **Personal Banking**: Provide crypto wallets with utility bill payment capabilities
- **Regulatory Approach**: Compliant financial services for emerging crypto market
- **Web-First Strategy**: Faster time-to-market with universal accessibility

## What

Web-based dual-account payment ecosystem with:

### Customer Web Portal (Responsive)
- BTC and USDT wallet management with mobile-optimized interface
- Buy BTC / USDT with M-Pesa, convert back to KES
- Pay utility bills (KPLC, Water, DSTV) directly from crypto
- P2P transfers and international remittances
- Progressive Web App (PWA) capabilities

### Merchant Dashboard (Next.js)
- Accept BTC/USDT payments with QR codes
- Automatic daily/weekly KES settlements to M-Pesa/Bank
- Sales analytics and tax reporting
- E-commerce API integration
- Multi-tier verification system

### Admin Panel (React)
- System monitoring and compliance oversight
- User management and KYC approval workflows
- Transaction monitoring and AML alerts
- Financial reporting and audit trails

### Success Criteria

- [ ] Responsive web interfaces work perfectly on mobile browsers
- [ ] USDT payments process with \u003c60 second confirmation
- [ ] M-Pesa settlements execute automatically within 2 hours
- [ ] Support 1000+ concurrent users with \u003c200ms response times
- [ ] 99.9% uptime with comprehensive monitoring
- [ ] Full CBK/CMA regulatory compliance
- [ ] Complete audit trail for all financial transactions
- [ ] Mobile-responsive design scores 95+ on Lighthouse




## AI Dev Tasks — Qpesapay Dual Account Crypto-Fiat Payment Ecosystem

This document outlines task-ready AI prompts to guide agents like Cursor, Devin, or Claude through the development of Qpesapay’s MVP using a hybrid Rust and C# microservices architecture.

---

### 🧭 System Overview

Qpesapay enables:

- **Personal Accounts**: Crypto wallet management, KES conversion, bill payments
- **Merchant Accounts**: Crypto acceptance with daily/weekly settlements
- **KES Settlements**: Instant settlement using **Yellowcard API** to M-Pesa
- **Chain Support**: BTC, USDT-ERC20, USDT-TRC20

---

### 🎯 AI Dev Tasks per Microservice

---

### 🔐 1. Authentication & Identity (C#)

```
TASK:
Build a secure Auth microservice using C#/.NET Core with support for JWT, OTP (Twilio or Africa’s Talking), and role-based access (personal vs merchant).
Include endpoints: /signup, /login, /verify-otp, /roles
```

---

### 🛂 2. KYC Verification Service (C#)

```
TASK:
Build a KYC verification microservice that integrates with Synaps or Smile Identity APIs. Handle image uploads (ID, passport, selfie) and return verified/unverified status.
Endpoints: /upload-id, /upload-selfie, /status
```

---

### 💰 3. Wallet Management (Rust)

```
TASK:
Create a Rust-based wallet engine to generate and manage HD wallets for BTC, USDT-ERC20, and USDT-TRC20. Include secure key handling and address derivation.
Supports both Personal and Merchant wallets.
```

---

### 🔍 4. Blockchain Listener (Rust)

```
TASK:
Write a high-performance blockchain listener in Rust that monitors incoming transactions on BTC, Ethereum (ERC20), and TRON (TRC20).
Trigger wallet balance updates upon confirmations.
```

---

### 💸 5. KES Settlement via Yellowcard (Rust)

```
TASK:
Create a Rust microservice that integrates with Yellowcard API to instantly convert crypto (BTC, USDT) to KES and initiate M-Pesa payouts.
Ensure retry logic and webhook confirmation.
```

---

### 🧾 6. Transaction Logging & Audit (C#)

```
TASK:
Build a logging service in C# to record all wallet activity, M-Pesa disbursements, and crypto settlements. Label each transaction with status and generate audit logs (CSV/PDF).
```

---

### 📲 7. Notification Engine (C#)

```
TASK:
Build a queue-based notification service to send SMS, email, and push updates using Africa’s Talking and SendGrid. Trigger notifications for deposits, payouts, KYC updates.
```

---

### 🏪 8. Merchant QR Payment Service (C#)

```
TASK:
Create a service that generates merchant QR codes with crypto values embedded. Accept scanned payments, update transaction status, and notify merchant backend via webhook.
```

---

### 🧑‍💻 9. Admin Dashboard & User Portal (Blazor or React)

```
TASK:
Develop web-based dashboards with full responsiveness. Personal users manage wallets, send crypto, and request M-Pesa payouts. Merchants monitor payments and settlements.
Include KYC status, transaction history, fraud alerts.
```

---

### 🔁 10. API Gateway (C#)

```
TASK:
Create a C# Backend-for-Frontend (BFF) that routes traffic securely to all microservices. Add API rate limiting, caching, and standardized response formatting for frontend consumption.
```

---

### ⚙️ Infrastructure Tasks

**Containerization:**

```
TASK:
Dockerize each microservice (C# and Rust). Define Dockerfiles and docker-compose.yaml for local testing.
```

**Database Setup:**

```
TASK:
Provision PostgreSQL databases for core services and Redis for caching.
Set up schemas for users, wallets, transactions, and KYC.
```

**Monitoring & Logging:**

```
TASK:
Integrate Prometheus and Grafana for metrics. Add centralized logging with Elastic or Loki.
```

**Secrets Management:**

```
TASK:
Use HashiCorp Vault or environment variables for secure key storage (JWT secrets, Yellowcard API keys, private keys).
```

---

### ✅ Success Milestones

-

---

Would you like this exported as `.md`, `.txt`, or `.json` for your AI agent queue?

