use async_trait::async_trait;
use std::sync::Arc;
use tokio::time::{sleep, Duration};
use tracing::{info, error, warn, debug};

use crate::database::Database;
use crate::error::Result;
use crate::models::{BitcoinTransaction, BitcoinOutput, BlockchainEvent, MonitoredAddress};
use crate::services::NotificationService;
use super::{BlockchainListener, ListenerStatus};

pub struct BitcoinListener {
    database: Database,
    notification_service: Arc<NotificationService>,
    status: Arc<tokio::sync::RwLock<ListenerStatus>>,
    poll_interval: Duration,
}

impl BitcoinListener {
    pub fn new(
        _rpc_url: String,
        _rpc_user: String,
        _rpc_password: String,
        database: Database,
        notification_service: Arc<NotificationService>,
    ) -> Self {
        let status = Arc::new(tokio::sync::RwLock::new(ListenerStatus {
            is_running: false,
            last_block_processed: None,
            last_error: None,
            processed_transactions: 0,
        }));

        Self {
            database,
            notification_service,
            status,
            poll_interval: Duration::from_secs(30),
        }
    }

    async fn get_monitored_addresses(&self) -> Result<Vec<MonitoredAddress>> {
        self.database.get_monitored_addresses("BTC").await
    }

    async fn process_new_blocks(&self) -> Result<()> {
        let mut status = self.status.write().await;

        let current_height = 800000u64; // Mock Bitcoin block height
        let start_height = status.last_block_processed
            .map(|h| h + 1)
            .unwrap_or(current_height.saturating_sub(10));

        info!("Processing Bitcoin blocks from {} to {} (mock)", start_height, current_height);

        for height in start_height..=current_height {
            if let Err(e) = self.process_block(height).await {
                error!("Error processing Bitcoin block {}: {}", height, e);
                status.last_error = Some(e.to_string());
                continue;
            }

            status.last_block_processed = Some(height);
        }

        Ok(())
    }

    async fn process_block(&self, height: u64) -> Result<()> {
        let monitored_addresses = self.get_monitored_addresses().await?;

        if height % 100 == 0 { // Every 100th block, simulate a transaction
            self.simulate_bitcoin_transaction(height, &monitored_addresses).await?;
        }

        Ok(())
    }

    async fn simulate_bitcoin_transaction(
        &self,
        block_height: u64,
        monitored_addresses: &[MonitoredAddress],
    ) -> Result<()> {
        if let Some(monitored) = monitored_addresses.first() {
            let bitcoin_tx = BitcoinTransaction {
                txid: format!("{:064x}", block_height * 1000),
                inputs: vec![],
                outputs: vec![BitcoinOutput {
                    value: 100000, // 0.001 BTC in satoshis
                    address: Some(monitored.address.clone()),
                    script_pubkey: "mock_script".to_string(),
                }],
                confirmations: 1,
                block_height: Some(block_height),
                timestamp: Some(chrono::Utc::now()),
                fee: 1000,
            };

            let event = BlockchainEvent::BitcoinTransaction(bitcoin_tx);

            self.database.store_blockchain_transaction(&event, monitored).await?;
            self.notification_service.send_transaction_notification(&event, monitored).await?;

            let mut status = self.status.write().await;
            status.processed_transactions += 1;

            info!("Simulated Bitcoin transaction to address: {}", monitored.address);
        }

        Ok(())
    }
}

#[async_trait]
impl BlockchainListener for BitcoinListener {
    async fn start(&self) -> Result<()> {
        info!("Starting Bitcoin listener");
        
        {
            let mut status = self.status.write().await;
            status.is_running = true;
        }

        loop {
            if let Err(e) = self.process_new_blocks().await {
                error!("Bitcoin listener error: {}", e);
                let mut status = self.status.write().await;
                status.last_error = Some(e.to_string());
            }

            sleep(self.poll_interval).await;
            
            // Check if we should continue running
            let status = self.status.read().await;
            if !status.is_running {
                break;
            }
        }

        info!("Bitcoin listener stopped");
        Ok(())
    }

    async fn stop(&self) -> Result<()> {
        info!("Stopping Bitcoin listener");
        let mut status = self.status.write().await;
        status.is_running = false;
        Ok(())
    }

    async fn get_status(&self) -> ListenerStatus {
        self.status.read().await.clone()
    }
}
