use std::sync::Arc;
use uuid::Uuid;
use chrono::Utc;
use rust_decimal::Decimal;

use crate::database::Database;
use crate::error::{AppError, Result};
use crate::models::*;
use crate::services::CryptoService;

pub struct WalletService {
    db: Database,
    crypto_service: Arc<CryptoService>,
}

impl WalletService {
    pub fn new(db: Database, crypto_service: Arc<CryptoService>) -> Self {
        Self {
            db,
            crypto_service,
        }
    }

    pub async fn create_wallet(&self, request: CreateWalletRequest) -> Result<WalletResponse> {
        // Validate currency
        let currency = Currency::from_string(&request.currency)
            .ok_or_else(|| AppError::WalletError("Unsupported currency".to_string()))?;

        // Check if wallet already exists for this user and currency
        let existing_wallets = self.db.get_user_wallets(request.user_id).await?;
        if existing_wallets.iter().any(|w| w.currency == request.currency) {
            return Err(AppError::WalletError(
                "Wallet already exists for this currency".to_string()
            ));
        }

        // Generate HD wallet
        let hd_wallet = self.crypto_service.create_hd_wallet(None)?;
        
        // Derive keypair for the wallet (account 0, index 0)
        let keypair = self.crypto_service.derive_keypair(&hd_wallet, &currency, 0, 0)?;

        // Encrypt private key
        let encrypted_private_key = self.crypto_service.encrypt_data(&keypair.private_key)?;

        // Create wallet record
        let wallet = Wallet {
            id: Uuid::new_v4(),
            user_id: request.user_id,
            currency: request.currency.clone(),
            address: keypair.address.clone(),
            private_key_encrypted: encrypted_private_key,
            public_key: Some(keypair.public_key),
            derivation_path: Some(format!("m/44'/{}'/{}'/{}/{}", 
                match currency {
                    Currency::Bitcoin => "1", // Testnet
                    Currency::UsdtErc20 => "60", // Ethereum
                    Currency::UsdtTrc20 => "195", // TRON
                },
                0, 0, 0
            )),
            balance: Decimal::ZERO,
            is_active: true,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        let created_wallet = self.db.create_wallet(&wallet).await?;

        // Create initial address record
        let initial_address = WalletAddress {
            id: Uuid::new_v4(),
            wallet_id: created_wallet.id,
            address: keypair.address,
            derivation_index: 0,
            is_used: false,
            created_at: Utc::now(),
        };

        self.db.create_address(&initial_address).await?;

        Ok(WalletResponse {
            id: created_wallet.id,
            user_id: created_wallet.user_id,
            currency: created_wallet.currency,
            address: created_wallet.address,
            balance: created_wallet.balance.to_string(),
            is_active: created_wallet.is_active,
            created_at: created_wallet.created_at,
        })
    }

    pub async fn get_user_wallets(&self, user_id: Uuid) -> Result<Vec<WalletResponse>> {
        let wallets = self.db.get_user_wallets(user_id).await?;
        
        Ok(wallets.into_iter().map(|w| WalletResponse {
            id: w.id,
            user_id: w.user_id,
            currency: w.currency,
            address: w.address,
            balance: w.balance.to_string(),
            is_active: w.is_active,
            created_at: w.created_at,
        }).collect())
    }

    pub async fn get_wallet_balance(&self, wallet_id: Uuid) -> Result<BalanceResponse> {
        let wallet = self.db.get_wallet_by_id(wallet_id).await?
            .ok_or_else(|| AppError::NotFound("Wallet not found".to_string()))?;

        // TODO: Implement real-time balance fetching from blockchain
        // For now, return the stored balance
        Ok(BalanceResponse {
            wallet_id: wallet.id,
            currency: wallet.currency,
            balance: wallet.balance.to_string(),
            confirmed_balance: wallet.balance.to_string(),
            unconfirmed_balance: "0".to_string(),
            last_updated: wallet.updated_at,
        })
    }

    pub async fn generate_new_address(&self, wallet_id: Uuid) -> Result<AddressResponse> {
        let wallet = self.db.get_wallet_by_id(wallet_id).await?
            .ok_or_else(|| AppError::NotFound("Wallet not found".to_string()))?;

        let currency = Currency::from_string(&wallet.currency)
            .ok_or_else(|| AppError::WalletError("Invalid currency".to_string()))?;

        // Get next derivation index
        let next_index = self.db.get_next_derivation_index(wallet_id).await?;

        // Decrypt master private key and derive new address
        let decrypted_private_key = self.crypto_service.decrypt_data(&wallet.private_key_encrypted)?;
        
        // For simplicity, we'll create a new HD wallet from the stored private key
        // In production, you'd want to store and reuse the HD wallet seed
        let hd_wallet = self.crypto_service.create_hd_wallet(None)?;
        let keypair = self.crypto_service.derive_keypair(&hd_wallet, &currency, 0, next_index as u32)?;

        let new_address = WalletAddress {
            id: Uuid::new_v4(),
            wallet_id,
            address: keypair.address.clone(),
            derivation_index: next_index,
            is_used: false,
            created_at: Utc::now(),
        };

        let created_address = self.db.create_address(&new_address).await?;

        Ok(AddressResponse {
            id: created_address.id,
            wallet_id: created_address.wallet_id,
            address: created_address.address,
            derivation_index: created_address.derivation_index,
            is_used: created_address.is_used,
            created_at: created_address.created_at,
        })
    }

    pub async fn get_wallet_addresses(
        &self, 
        wallet_id: Uuid, 
        limit: Option<i64>, 
        offset: Option<i64>
    ) -> Result<Vec<AddressResponse>> {
        let limit = limit.unwrap_or(50).min(100); // Max 100 addresses per request
        let offset = offset.unwrap_or(0);

        let addresses = self.db.get_wallet_addresses(wallet_id, limit, offset).await?;

        Ok(addresses.into_iter().map(|a| AddressResponse {
            id: a.id,
            wallet_id: a.wallet_id,
            address: a.address,
            derivation_index: a.derivation_index,
            is_used: a.is_used,
            created_at: a.created_at,
        }).collect())
    }

    pub async fn get_wallet_transactions(
        &self,
        wallet_id: Uuid,
        limit: Option<i64>,
        offset: Option<i64>
    ) -> Result<Vec<TransactionResponse>> {
        let limit = limit.unwrap_or(50).min(100);
        let offset = offset.unwrap_or(0);

        let transactions = self.db.get_wallet_transactions(wallet_id, limit, offset).await?;

        Ok(transactions.into_iter().map(|t| TransactionResponse {
            id: t.id,
            transaction_hash: t.transaction_hash,
            transaction_type: t.transaction_type,
            currency: t.currency,
            amount: t.amount.to_string(),
            fee: t.fee.to_string(),
            from_address: t.from_address,
            to_address: t.to_address,
            status: t.status,
            confirmations: t.confirmations,
            block_number: t.block_number,
            network: t.network,
            created_at: t.created_at,
            confirmed_at: t.confirmed_at,
        }).collect())
    }

    pub async fn send_transaction(
        &self,
        wallet_id: Uuid,
        request: SendTransactionRequest,
    ) -> Result<TransactionResponse> {
        let wallet = self.db.get_wallet_by_id(wallet_id).await?
            .ok_or_else(|| AppError::NotFound("Wallet not found".to_string()))?;

        let currency = Currency::from_string(&wallet.currency)
            .ok_or_else(|| AppError::WalletError("Invalid currency".to_string()))?;

        // Parse amount
        let amount = request.amount.parse::<Decimal>()
            .map_err(|_| AppError::BadRequest("Invalid amount".to_string()))?;

        if amount <= Decimal::ZERO {
            return Err(AppError::BadRequest("Amount must be positive".to_string()));
        }

        // Check balance
        if wallet.balance < amount {
            return Err(AppError::WalletError("Insufficient balance".to_string()));
        }

        // Create transaction record
        let transaction = Transaction {
            id: Uuid::new_v4(),
            user_id: Some(wallet.user_id),
            wallet_id: Some(wallet_id),
            transaction_hash: None, // Will be set after broadcasting
            transaction_type: "withdrawal".to_string(),
            currency: wallet.currency.clone(),
            amount,
            fee: Decimal::ZERO, // Will be calculated
            from_address: Some(wallet.address.clone()),
            to_address: Some(request.to_address.clone()),
            status: "pending".to_string(),
            confirmations: 0,
            block_number: None,
            network: Some(match currency {
                Currency::Bitcoin => "bitcoin".to_string(),
                Currency::UsdtErc20 => "ethereum".to_string(),
                Currency::UsdtTrc20 => "tron".to_string(),
            }),
            created_at: Utc::now(),
            confirmed_at: None,
        };

        // TODO: Implement actual blockchain transaction broadcasting
        // For now, just create the transaction record
        let created_transaction = self.db.create_transaction(&transaction).await?;

        Ok(TransactionResponse {
            id: created_transaction.id,
            transaction_hash: created_transaction.transaction_hash,
            transaction_type: created_transaction.transaction_type,
            currency: created_transaction.currency,
            amount: created_transaction.amount.to_string(),
            fee: created_transaction.fee.to_string(),
            from_address: created_transaction.from_address,
            to_address: created_transaction.to_address,
            status: created_transaction.status,
            confirmations: created_transaction.confirmations,
            block_number: created_transaction.block_number,
            network: created_transaction.network,
            created_at: created_transaction.created_at,
            confirmed_at: created_transaction.confirmed_at,
        })
    }
}
