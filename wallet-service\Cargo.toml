[package]
name = "qpesapay-wallet-service"
version = "0.1.0"
edition = "2021"

[dependencies]
# Web framework
tokio = { version = "1.0", features = ["full"] }
axum = "0.7"
tower = "0.4"
tower-http = { version = "0.5", features = ["cors", "trace"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Database
sqlx = { version = "0.8", features = ["runtime-tokio-rustls", "postgres", "uuid", "chrono", "json"], default-features = false }
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }

# Cryptography and blockchain
bitcoin = "0.30"
secp256k1 = { version = "0.27", features = ["rand", "recovery"] }
bip39 = "1.0"
hex = "0.4"
sha2 = "0.10"
aes-gcm = "0.10"
rand = "0.8"
rust_decimal = "1.0"

# Ethereum (simplified)
ethers-core = "1.0"

# TRON (using custom implementation)
base58 = "0.2"
sha3 = "0.10"

# HTTP client
reqwest = { version = "0.12", features = ["json"] }

# Logging and tracing
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# Configuration
config = "0.14"
dotenv = "0.15"

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Async traits
async-trait = "0.1"

# JSON Web Tokens
jsonwebtoken = "9.0"

# Environment variables
envy = "0.4"

[dev-dependencies]
# Testing
tokio-test = "0.4"
mockall = "0.13"
tempfile = "3.0"
testcontainers = "0.23"
testcontainers-modules = { version = "0.11", features = ["postgres"] }
serial_test = "3.0"
