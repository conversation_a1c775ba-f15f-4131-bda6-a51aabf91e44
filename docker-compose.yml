version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: qpesapay-postgres
    environment:
      POSTGRES_DB: qpesapay_db
      POSTGRES_USER: qpesapay_user
      POSTGRES_PASSWORD: qpesapay_password_change_in_production
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - qpesapay-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U qpesapay_user -d qpesapay_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: qpesapay-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - qpesapay-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Authentication Service
  auth-service:
    build:
      context: ./Qpesapay.AuthService
      dockerfile: Dockerfile
    container_name: qpesapay-auth-service
    ports:
      - "5001:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=qpesapay_db;Username=qpesapay_user;Password=qpesapay_password_change_in_production
      - JwtSettings__Secret=your-jwt-secret-key-here-change-in-production-min-32-chars
      - JwtSettings__Issuer=Qpesapay.AuthService
      - JwtSettings__Audience=Qpesapay.Users
      - JwtSettings__ExpirationInMinutes=1440
      - OtpSettings__Provider=AfricaTalking
      - OtpSettings__ApiKey=atsk_ed1124db20b14a8dcdfd460ea90fe670b053e6a1d77b86e51fd96c47809959254de7839e
      - OtpSettings__Username=sandbox
      - Redis__ConnectionString=redis:6379
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - qpesapay-network

  # KYC Service
  kyc-service:
    build:
      context: ./Qpesapay.KycService
      dockerfile: Dockerfile
    container_name: qpesapay-kyc-service
    ports:
      - "5002:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=qpesapay_db;Username=qpesapay_user;Password=qpesapay_password_change_in_production
      - KycSettings__Provider=SmileIdentity
      - KycSettings__ApiKey=your-smile-identity-api-key
      - KycSettings__PartnerId=your-smile-identity-partner-id
      - FileStorage__BasePath=/app/uploads
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - kyc_uploads:/app/uploads
    networks:
      - qpesapay-network

  wallet-service:
    build:
      context: ./wallet-service
      dockerfile: Dockerfile
    container_name: qpesapay-wallet-service
    ports:
      - "5003:8080"
    environment:
      - RUST_LOG=info
      - DATABASE_URL=*****************************************************************************/qpesapay_db
      - PORT=8080
      - ENCRYPTION_KEY=your-encryption-key-32-chars-min-change-in-production
      - JWT_SECRET=your-jwt-secret-key-change-in-production
      - BITCOIN_NETWORK=testnet
      - ETHEREUM_RPC_URL=https://goerli.infura.io/v3/your-infura-project-id
      - TRON_RPC_URL=https://api.shasta.trongrid.io
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - qpesapay-network

  blockchain-listener:
    build:
      context: ./blockchain-listener
      dockerfile: Dockerfile
    container_name: qpesapay-blockchain-listener
    ports:
      - "5004:8081"
    environment:
      - RUST_LOG=info
      - DATABASE_URL=*****************************************************************************/qpesapay_db
      - PORT=8081
      # Bitcoin configuration
      - BITCOIN_RPC_URL=http://bitcoin-testnet:18332
      - BITCOIN_RPC_USER=bitcoin
      - BITCOIN_RPC_PASSWORD=password
      - BITCOIN_NETWORK=testnet
      # Ethereum configuration (using public testnet)
      - ETHEREUM_WS_URL=wss://sepolia.infura.io/ws/v3/YOUR_PROJECT_ID
      - ETHEREUM_RPC_URL=https://sepolia.infura.io/v3/YOUR_PROJECT_ID
      - USDT_ERC20_CONTRACT=******************************************
      # TRON configuration (using testnet)
      - TRON_API_URL=https://api.shasta.trongrid.io
      - USDT_TRC20_CONTRACT=TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t
      # Service URLs
      - WALLET_SERVICE_URL=http://wallet-service:8080
      - NOTIFICATION_WEBHOOK_URL=http://notification-service:8082/webhook
      # Monitoring settings
      - CONFIRMATION_BLOCKS=6
      - POLL_INTERVAL_SECONDS=30
    depends_on:
      postgres:
        condition: service_healthy
      wallet-service:
        condition: service_started
    networks:
      - qpesapay-network

  settlement-service:
    build:
      context: ./settlement-service
      dockerfile: Dockerfile
    container_name: qpesapay-settlement-service
    ports:
      - "5005:8000"
    environment:
      - RUST_LOG=info
      - DATABASE_URL=*****************************************************************************/qpesapay_db
      - PORT=8000
      - YELLOWCARD_API_KEY=mock-yellowcard-api-key
      - YELLOWCARD_API_SECRET=mock-yellowcard-api-secret
      - YELLOWCARD_BASE_URL=https://api.yellowcard.io/v1
      - YELLOWCARD_WEBHOOK_SECRET=mock-webhook-secret
      - EXCHANGE_RATE_API_KEY=free-tier-coingecko
      - COINGECKO_BASE_URL=https://api.coingecko.com/api/v3
      - AFRICAS_TALKING_API_KEY=${AFRICAS_TALKING_API_KEY}
      - AFRICAS_TALKING_USERNAME=${AFRICAS_TALKING_USERNAME}
      - SETTLEMENT_WEBHOOK_URL=http://notification-service:8082/webhook/settlement
      - MAX_SETTLEMENT_AMOUNT=1000000.00
      - MIN_SETTLEMENT_AMOUNT=100.00
      - EXCHANGE_RATE_UPDATE_INTERVAL=300
      - SETTLEMENT_BATCH_SIZE=10
      - SETTLEMENT_RETRY_ATTEMPTS=3
      - SETTLEMENT_TIMEOUT_SECONDS=30
    depends_on:
      postgres:
        condition: service_healthy
      wallet-service:
        condition: service_started
      blockchain-listener:
        condition: service_started
    networks:
      - qpesapay-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
  redis_data:
  kyc_uploads:

networks:
  qpesapay-network:
    driver: bridge
