version: '3.8'

services:
  # Test PostgreSQL Database
  postgres-test:
    image: postgres:15-alpine
    container_name: qpesapay-postgres-test
    environment:
      POSTGRES_DB: qpesapay_test_db
      POSTGRES_USER: test_user
      POSTGRES_PASSWORD: test_password
    ports:
      - "5433:5432"
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - qpesapay-test-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U test_user -d qpesapay_test_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Test Redis Cache
  redis-test:
    image: redis:7-alpine
    container_name: qpesapay-redis-test
    ports:
      - "6380:6379"
    networks:
      - qpesapay-test-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Mock Yellowcard API for testing
  yellowcard-mock:
    image: wiremock/wiremock:3.3.1
    container_name: qpesapay-yellowcard-mock
    ports:
      - "8089:8080"
    volumes:
      - ./tests/mocks/yellowcard:/home/<USER>
    networks:
      - qpesapay-test-network
    command: ["--global-response-templating", "--verbose"]

  # Mock Africa's Talking API for testing
  africastalking-mock:
    image: wiremock/wiremock:3.3.1
    container_name: qpesapay-africastalking-mock
    ports:
      - "8088:8080"
    volumes:
      - ./tests/mocks/africastalking:/home/<USER>
    networks:
      - qpesapay-test-network
    command: ["--global-response-templating", "--verbose"]

  # Test Bitcoin Node (regtest)
  bitcoin-test:
    image: ruimarinho/bitcoin-core:23
    container_name: qpesapay-bitcoin-test
    ports:
      - "18443:18443"
      - "18444:18444"
    volumes:
      - bitcoin_test_data:/home/<USER>/.bitcoin
    networks:
      - qpesapay-test-network
    command: >
      bitcoind
      -regtest
      -server
      -rpcallowip=0.0.0.0/0
      -rpcuser=test_user
      -rpcpassword=test_password
      -rpcport=18443
      -port=18444
      -fallbackfee=0.0002
      -txindex=1

  # Test runner service
  test-runner:
    build:
      context: .
      dockerfile: tests/Dockerfile.test
    container_name: qpesapay-test-runner
    environment:
      - DATABASE_URL=*******************************************************/qpesapay_test_db
      - REDIS_URL=redis://redis-test:6379
      - YELLOWCARD_BASE_URL=http://yellowcard-mock:8080
      - AFRICASTALKING_BASE_URL=http://africastalking-mock:8080
      - BITCOIN_RPC_URL=http://bitcoin-test:18443
      - BITCOIN_RPC_USER=test_user
      - BITCOIN_RPC_PASSWORD=test_password
      - RUST_LOG=debug
      - ASPNETCORE_ENVIRONMENT=Testing
    depends_on:
      postgres-test:
        condition: service_healthy
      redis-test:
        condition: service_healthy
      yellowcard-mock:
        condition: service_started
      africastalking-mock:
        condition: service_started
    networks:
      - qpesapay-test-network
    volumes:
      - .:/workspace
    working_dir: /workspace
    command: ["./scripts/run-tests.ps1", "-TestType", "all", "-Verbose"]

volumes:
  postgres_test_data:
  bitcoin_test_data:

networks:
  qpesapay-test-network:
    driver: bridge
