{"format": 1, "restore": {"C:\\Users\\<USER>\\OneDrive\\Desktop\\Work\\New\\Qpesapay.AuthService.Tests\\Qpesapay.AuthService.Tests.csproj": {}}, "projects": {"C:\\Users\\<USER>\\OneDrive\\Desktop\\Work\\New\\Qpesapay.AuthService.Tests\\Qpesapay.AuthService.Tests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Work\\New\\Qpesapay.AuthService.Tests\\Qpesapay.AuthService.Tests.csproj", "projectName": "Qpesapay.AuthService.Tests", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Work\\New\\Qpesapay.AuthService.Tests\\Qpesapay.AuthService.Tests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Work\\New\\Qpesapay.AuthService.Tests\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\OneDrive\\Desktop\\Work\\New\\Qpesapay.AuthService\\Qpesapay.AuthService.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Work\\New\\Qpesapay.AuthService\\Qpesapay.AuthService.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"FluentAssertions": {"target": "Package", "version": "[6.12.1, )"}, "Microsoft.AspNetCore.Mvc.Testing": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.InMemory": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.11.1, )"}, "Moq": {"target": "Package", "version": "[4.20.72, )"}, "Testcontainers.PostgreSql": {"target": "Package", "version": "[3.11.0, )"}, "coverlet.collector": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[6.0.2, )"}, "xunit": {"target": "Package", "version": "[2.9.2, )"}, "xunit.runner.visualstudio": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[2.8.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\Work\\New\\Qpesapay.AuthService\\Qpesapay.AuthService.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Work\\New\\Qpesapay.AuthService\\Qpesapay.AuthService.csproj", "projectName": "Qpesapay.AuthService", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Work\\New\\Qpesapay.AuthService\\Qpesapay.AuthService.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Work\\New\\Qpesapay.AuthService\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[8.0.0, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[8.0.0, )"}, "StackExchange.Redis": {"target": "Package", "version": "[2.8.16, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[7.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}