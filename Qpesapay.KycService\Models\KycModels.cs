using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Qpesapay.KycService.Models;

[Table("profiles", Schema = "kyc")]
public class KycProfile
{
    [Key]
    [Column("id")]
    public Guid Id { get; set; } = Guid.NewGuid();

    [Required]
    [Column("user_id")]
    public Guid UserId { get; set; }

    [MaxLength(100)]
    [Column("first_name")]
    public string? FirstName { get; set; }

    [MaxLength(100)]
    [Column("last_name")]
    public string? LastName { get; set; }

    [Column("date_of_birth")]
    public DateTime? DateOfBirth { get; set; }

    [MaxLength(50)]
    [Column("nationality")]
    public string? Nationality { get; set; }

    [MaxLength(50)]
    [Column("id_number")]
    public string? IdNumber { get; set; }

    [MaxLength(20)]
    [Column("id_type")]
    public string? IdType { get; set; } // national_id, passport, driving_license

    [Column("address")]
    public string? Address { get; set; }

    [MaxLength(100)]
    [Column("city")]
    public string? City { get; set; }

    [MaxLength(50)]
    [Column("country")]
    public string Country { get; set; } = "Kenya";

    [MaxLength(20)]
    [Column("verification_status")]
    public string VerificationStatus { get; set; } = "pending"; // pending, in_review, verified, rejected

    [Column("verification_level")]
    public int VerificationLevel { get; set; } = 1; // 1, 2, 3

    [Column("created_at")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    [Column("updated_at")]
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    // Navigation properties
    public virtual ICollection<KycDocument> Documents { get; set; } = new List<KycDocument>();
}

[Table("documents", Schema = "kyc")]
public class KycDocument
{
    [Key]
    [Column("id")]
    public Guid Id { get; set; } = Guid.NewGuid();

    [Required]
    [Column("profile_id")]
    public Guid ProfileId { get; set; }

    [Required]
    [MaxLength(50)]
    [Column("document_type")]
    public string DocumentType { get; set; } = string.Empty; // id_front, id_back, selfie, proof_of_address

    [Required]
    [MaxLength(500)]
    [Column("file_path")]
    public string FilePath { get; set; } = string.Empty;

    [Column("file_size")]
    public int? FileSize { get; set; }

    [MaxLength(100)]
    [Column("mime_type")]
    public string? MimeType { get; set; }

    [MaxLength(20)]
    [Column("verification_status")]
    public string VerificationStatus { get; set; } = "pending"; // pending, verified, rejected

    [Column("verification_notes")]
    public string? VerificationNotes { get; set; }

    [Column("uploaded_at")]
    public DateTime UploadedAt { get; set; } = DateTime.UtcNow;

    [Column("verified_at")]
    public DateTime? VerifiedAt { get; set; }

    // Navigation properties
    [ForeignKey("ProfileId")]
    public virtual KycProfile Profile { get; set; } = null!;
}

// DTOs for API requests/responses
public record CreateKycProfileRequest(
    Guid UserId,
    string FirstName,
    string LastName,
    DateTime DateOfBirth,
    string Nationality,
    string IdNumber,
    string IdType,
    string Address,
    string City,
    string Country = "Kenya"
);

public record UpdateKycProfileRequest(
    string? FirstName,
    string? LastName,
    DateTime? DateOfBirth,
    string? Nationality,
    string? IdNumber,
    string? IdType,
    string? Address,
    string? City,
    string? Country
);

public record KycProfileResponse(
    Guid Id,
    Guid UserId,
    string? FirstName,
    string? LastName,
    DateTime? DateOfBirth,
    string? Nationality,
    string? IdNumber,
    string? IdType,
    string? Address,
    string? City,
    string Country,
    string VerificationStatus,
    int VerificationLevel,
    DateTime CreatedAt,
    DateTime UpdatedAt,
    List<KycDocumentResponse> Documents
);

public record KycDocumentResponse(
    Guid Id,
    string DocumentType,
    string FilePath,
    int? FileSize,
    string? MimeType,
    string VerificationStatus,
    string? VerificationNotes,
    DateTime UploadedAt,
    DateTime? VerifiedAt
);

public record DocumentUploadRequest(
    Guid ProfileId,
    string DocumentType
);

public record VerificationStatusUpdate(
    string Status,
    string? Notes
);

public enum DocumentType
{
    IdFront,
    IdBack,
    Selfie,
    ProofOfAddress
}

public enum VerificationStatus
{
    Pending,
    InReview,
    Verified,
    Rejected
}

public enum IdType
{
    NationalId,
    Passport,
    DrivingLicense
}
