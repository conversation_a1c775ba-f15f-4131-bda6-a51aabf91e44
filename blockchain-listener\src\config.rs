use serde::Deserialize;
use std::env;
use crate::error::{AppError, Result};

#[derive(Debug, <PERSON>lone, Deserialize)]
pub struct Config {
    pub database_url: String,
    pub port: u16,
    
    // Bitcoin configuration
    pub bitcoin_rpc_url: String,
    pub bitcoin_rpc_user: String,
    pub bitcoin_rpc_password: String,
    pub bitcoin_network: String,
    
    // Ethereum configuration
    pub ethereum_ws_url: String,
    pub ethereum_rpc_url: String,
    pub usdt_erc20_contract: String,
    
    // TRON configuration
    pub tron_api_url: String,
    pub usdt_trc20_contract: String,
    
    // Notification service
    pub wallet_service_url: String,
    pub notification_webhook_url: String,
    
    // Monitoring
    pub confirmation_blocks: u32,
    pub poll_interval_seconds: u64,
}

impl Config {
    pub fn from_env() -> Result<Self> {
        dotenv::dotenv().ok();

        let config = Config {
            database_url: env::var("DATABASE_URL")
                .unwrap_or_else(|_| "postgresql://qpesapay_user:qpesapay_password_change_in_production@localhost:5432/qpesapay_db".to_string()),
            port: env::var("PORT")
                .unwrap_or_else(|_| "8081".to_string())
                .parse()
                .map_err(|_| AppError::ConfigError("Invalid port number".to_string()))?,
            
            // Bitcoin
            bitcoin_rpc_url: env::var("BITCOIN_RPC_URL")
                .unwrap_or_else(|_| "http://localhost:18332".to_string()), // Testnet default
            bitcoin_rpc_user: env::var("BITCOIN_RPC_USER")
                .unwrap_or_else(|_| "bitcoin".to_string()),
            bitcoin_rpc_password: env::var("BITCOIN_RPC_PASSWORD")
                .unwrap_or_else(|_| "password".to_string()),
            bitcoin_network: env::var("BITCOIN_NETWORK")
                .unwrap_or_else(|_| "testnet".to_string()),
            
            // Ethereum
            ethereum_ws_url: env::var("ETHEREUM_WS_URL")
                .unwrap_or_else(|_| "wss://sepolia.infura.io/ws/v3/YOUR_PROJECT_ID".to_string()),
            ethereum_rpc_url: env::var("ETHEREUM_RPC_URL")
                .unwrap_or_else(|_| "https://sepolia.infura.io/v3/YOUR_PROJECT_ID".to_string()),
            usdt_erc20_contract: env::var("USDT_ERC20_CONTRACT")
                .unwrap_or_else(|_| "******************************************".to_string()), // Mainnet USDT
            
            // TRON
            tron_api_url: env::var("TRON_API_URL")
                .unwrap_or_else(|_| "https://api.shasta.trongrid.io".to_string()), // Testnet
            usdt_trc20_contract: env::var("USDT_TRC20_CONTRACT")
                .unwrap_or_else(|_| "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t".to_string()), // Mainnet USDT
            
            // Services
            wallet_service_url: env::var("WALLET_SERVICE_URL")
                .unwrap_or_else(|_| "http://localhost:8080".to_string()),
            notification_webhook_url: env::var("NOTIFICATION_WEBHOOK_URL")
                .unwrap_or_else(|_| "http://localhost:8082/webhook".to_string()),
            
            // Monitoring
            confirmation_blocks: env::var("CONFIRMATION_BLOCKS")
                .unwrap_or_else(|_| "6".to_string())
                .parse()
                .unwrap_or(6),
            poll_interval_seconds: env::var("POLL_INTERVAL_SECONDS")
                .unwrap_or_else(|_| "30".to_string())
                .parse()
                .unwrap_or(30),
        };

        Ok(config)
    }
}
