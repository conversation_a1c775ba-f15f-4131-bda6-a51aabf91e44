-- Qpesapay Database Schema
-- PostgreSQL initialization script

-- Note: Database is already created by Docker environment variables
-- Connect to the database
\c qpesapay_db;

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create schemas for different services
CREATE SCHEMA IF NOT EXISTS auth;
CREATE SCHEMA IF NOT EXISTS kyc;
CREATE SCHEMA IF NOT EXISTS wallet;
CREATE SCHEMA IF NOT EXISTS transaction;
CREATE SCHEMA IF NOT EXISTS audit;
CREATE SCHEMA IF NOT EXISTS notification;

-- =============================================
-- AUTH SCHEMA TABLES
-- =============================================

-- Users table
CREATE TABLE auth.users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    phone_number VARCHAR(20),
    role VARCHAR(20) NOT NULL CHECK (role IN ('personal', 'merchant')),
    is_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- OTP codes table
CREATE TABLE auth.otp_codes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    otp_code VARCHAR(10) NOT NULL,
    purpose VARCHAR(50) NOT NULL, -- 'signup', 'login', 'reset_password'
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    used_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- User sessions table
CREATE TABLE auth.user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_used_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- KYC SCHEMA TABLES
-- =============================================

-- KYC profiles table
CREATE TABLE kyc.profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    date_of_birth DATE,
    nationality VARCHAR(50),
    id_number VARCHAR(50),
    id_type VARCHAR(20) CHECK (id_type IN ('national_id', 'passport', 'driving_license')),
    address TEXT,
    city VARCHAR(100),
    country VARCHAR(50) DEFAULT 'Kenya',
    verification_status VARCHAR(20) DEFAULT 'pending' CHECK (verification_status IN ('pending', 'in_review', 'verified', 'rejected')),
    verification_level INTEGER DEFAULT 1 CHECK (verification_level BETWEEN 1 AND 3),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- KYC documents table
CREATE TABLE kyc.documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    profile_id UUID REFERENCES kyc.profiles(id) ON DELETE CASCADE,
    document_type VARCHAR(50) NOT NULL, -- 'id_front', 'id_back', 'selfie', 'proof_of_address'
    file_path VARCHAR(500) NOT NULL,
    file_size INTEGER,
    mime_type VARCHAR(100),
    verification_status VARCHAR(20) DEFAULT 'pending' CHECK (verification_status IN ('pending', 'verified', 'rejected')),
    verification_notes TEXT,
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    verified_at TIMESTAMP WITH TIME ZONE
);

-- =============================================
-- WALLET SCHEMA TABLES
-- =============================================

-- Wallets table
CREATE TABLE wallet.wallets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    currency VARCHAR(10) NOT NULL, -- 'BTC', 'USDT_ERC20', 'USDT_TRC20'
    address VARCHAR(255) NOT NULL,
    private_key_encrypted TEXT NOT NULL, -- Encrypted private key
    public_key TEXT,
    derivation_path VARCHAR(100),
    balance DECIMAL(18, 8) DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, currency)
);

-- Wallet addresses table (for HD wallets)
CREATE TABLE wallet.addresses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    wallet_id UUID REFERENCES wallet.wallets(id) ON DELETE CASCADE,
    address VARCHAR(255) NOT NULL UNIQUE,
    derivation_index INTEGER NOT NULL,
    is_used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- TRANSACTION SCHEMA TABLES
-- =============================================

-- Transactions table
CREATE TABLE transaction.transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id),
    wallet_id UUID REFERENCES wallet.wallets(id),
    transaction_hash VARCHAR(255),
    transaction_type VARCHAR(20) NOT NULL, -- 'deposit', 'withdrawal', 'transfer', 'settlement'
    currency VARCHAR(10) NOT NULL,
    amount DECIMAL(18, 8) NOT NULL,
    fee DECIMAL(18, 8) DEFAULT 0,
    from_address VARCHAR(255),
    to_address VARCHAR(255),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'failed', 'cancelled')),
    confirmations INTEGER DEFAULT 0,
    block_number BIGINT,
    network VARCHAR(20), -- 'bitcoin', 'ethereum', 'tron'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    confirmed_at TIMESTAMP WITH TIME ZONE
);

-- Settlements table (KES payouts)
CREATE TABLE transaction.settlements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id),
    transaction_id UUID REFERENCES transaction.transactions(id),
    crypto_amount DECIMAL(18, 8) NOT NULL,
    crypto_currency VARCHAR(10) NOT NULL,
    kes_amount DECIMAL(12, 2) NOT NULL,
    exchange_rate DECIMAL(12, 4) NOT NULL,
    yellowcard_transaction_id VARCHAR(255),
    mpesa_phone VARCHAR(20),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE
);

-- =============================================
-- AUDIT SCHEMA TABLES
-- =============================================

-- Audit logs table
CREATE TABLE audit.logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id),
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- NOTIFICATION SCHEMA TABLES
-- =============================================

-- Notifications table
CREATE TABLE notification.notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id),
    type VARCHAR(50) NOT NULL, -- 'sms', 'email', 'push'
    subject VARCHAR(255),
    message TEXT NOT NULL,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'failed')),
    sent_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================

-- Auth indexes
CREATE INDEX idx_users_email ON auth.users(email);
CREATE INDEX idx_users_phone ON auth.users(phone_number);
CREATE INDEX idx_otp_codes_email_expires ON auth.otp_codes(email, expires_at);
CREATE INDEX idx_user_sessions_user_id ON auth.user_sessions(user_id);

-- KYC indexes
CREATE INDEX idx_profiles_user_id ON kyc.profiles(user_id);
CREATE INDEX idx_profiles_verification_status ON kyc.profiles(verification_status);
CREATE INDEX idx_documents_profile_id ON kyc.documents(profile_id);

-- Wallet indexes
CREATE INDEX idx_wallets_user_id ON wallet.wallets(user_id);
CREATE INDEX idx_wallets_currency ON wallet.wallets(currency);
CREATE INDEX idx_addresses_wallet_id ON wallet.addresses(wallet_id);
CREATE INDEX idx_addresses_address ON wallet.addresses(address);

-- Transaction indexes
CREATE INDEX idx_transactions_user_id ON transaction.transactions(user_id);
CREATE INDEX idx_transactions_hash ON transaction.transactions(transaction_hash);
CREATE INDEX idx_transactions_status ON transaction.transactions(status);
CREATE INDEX idx_transactions_created_at ON transaction.transactions(created_at);
CREATE INDEX idx_settlements_user_id ON transaction.settlements(user_id);

-- Audit indexes
CREATE INDEX idx_audit_logs_user_id ON audit.logs(user_id);
CREATE INDEX idx_audit_logs_created_at ON audit.logs(created_at);

-- Notification indexes
CREATE INDEX idx_notifications_user_id ON notification.notifications(user_id);
CREATE INDEX idx_notifications_status ON notification.notifications(status);

-- =============================================
-- FUNCTIONS AND TRIGGERS
-- =============================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON auth.users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON kyc.profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_wallets_updated_at BEFORE UPDATE ON wallet.wallets FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
