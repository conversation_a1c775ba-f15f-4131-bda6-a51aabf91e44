use axum::{
    http::StatusCode,
    response::{IntoResponse, Response},
    J<PERSON>,
};
use serde_json::json;
use thiserror::Error;

pub type Result<T> = std::result::Result<T, AppError>;

#[derive(Error, Debug)]
pub enum AppError {
    #[error("Database error: {0}")]
    Database(#[from] sqlx::Error),

    #[error("HTTP client error: {0}")]
    Http(#[from] reqwest::Error),

    #[error("Serialization error: {0}")]
    Serialization(#[from] serde_json::Error),

    #[error("Configuration error: {0}")]
    Config(String),

    #[error("Settlement error: {0}")]
    Settlement(String),

    #[error("Exchange rate error: {0}")]
    ExchangeRate(String),

    #[error("Validation error: {0}")]
    Validation(String),

    #[error("Not found: {0}")]
    NotFound(String),

    #[error("Unauthorized: {0}")]
    Unauthorized(String),

    #[error("Payment processing error: {0}")]
    PaymentProcessing(String),

    #[error("External API error: {0}")]
    ExternalApi(String),

    #[error("Parse error: {0}")]
    Parse(String),

    #[error("Internal server error: {0}")]
    Internal(String),

    #[error("Timeout error: {0}")]
    Timeout(String),

    #[error("Rate limit exceeded: {0}")]
    RateLimit(String),

    #[error("Insufficient funds: {0}")]
    InsufficientFunds(String),

    #[error("Invalid amount: {0}")]
    InvalidAmount(String),

    #[error("Unsupported currency: {0}")]
    UnsupportedCurrency(String),
}

impl IntoResponse for AppError {
    fn into_response(self) -> Response {
        let (status, error_message) = match &self {
            AppError::Database(_) => (StatusCode::INTERNAL_SERVER_ERROR, "Database error"),
            AppError::Http(_) => (StatusCode::BAD_GATEWAY, "External service error"),
            AppError::Serialization(_) => (StatusCode::BAD_REQUEST, "Invalid request format"),
            AppError::Config(_) => (StatusCode::INTERNAL_SERVER_ERROR, "Configuration error"),
            AppError::Settlement(_) => (StatusCode::UNPROCESSABLE_ENTITY, "Settlement processing error"),
            AppError::ExchangeRate(_) => (StatusCode::SERVICE_UNAVAILABLE, "Exchange rate service error"),
            AppError::Validation(_) => (StatusCode::BAD_REQUEST, "Validation error"),
            AppError::NotFound(_) => (StatusCode::NOT_FOUND, "Resource not found"),
            AppError::Unauthorized(_) => (StatusCode::UNAUTHORIZED, "Unauthorized"),
            AppError::PaymentProcessing(_) => (StatusCode::UNPROCESSABLE_ENTITY, "Payment processing error"),
            AppError::ExternalApi(_) => (StatusCode::BAD_GATEWAY, "External API error"),
            AppError::Parse(_) => (StatusCode::BAD_REQUEST, "Parse error"),
            AppError::Internal(_) => (StatusCode::INTERNAL_SERVER_ERROR, "Internal server error"),
            AppError::Timeout(_) => (StatusCode::REQUEST_TIMEOUT, "Request timeout"),
            AppError::RateLimit(_) => (StatusCode::TOO_MANY_REQUESTS, "Rate limit exceeded"),
            AppError::InsufficientFunds(_) => (StatusCode::UNPROCESSABLE_ENTITY, "Insufficient funds"),
            AppError::InvalidAmount(_) => (StatusCode::BAD_REQUEST, "Invalid amount"),
            AppError::UnsupportedCurrency(_) => (StatusCode::BAD_REQUEST, "Unsupported currency"),
        };

        let body = Json(json!({
            "error": error_message,
            "message": self.to_string(),
            "timestamp": chrono::Utc::now()
        }));

        (status, body).into_response()
    }
}
