using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using FluentAssertions;
using Xunit;
using Qpesapay.AuthService.Data;
using Qpesapay.AuthService.Models;
using Qpesapay.AuthService.Services;

namespace Qpesapay.AuthService.Tests.Services;

public class UserServiceTests : IDisposable
{
    private readonly AuthDbContext _context;
    private readonly Mock<ILogger<UserService>> _loggerMock;
    private readonly UserService _userService;

    public UserServiceTests()
    {
        var options = new DbContextOptionsBuilder<AuthDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new AuthDbContext(options);
        _loggerMock = new Mock<ILogger<UserService>>();
        _userService = new UserService(_context, _loggerMock.Object);
    }

    [Fact]
    public async Task CreateUserAsync_ValidRequest_ShouldCreateUser()
    {
        // Arrange
        var request = new SignupRequest(
            "<EMAIL>",
            "SecurePassword123!",
            "+************",
            "personal"
        );

        // Act
        var result = await _userService.CreateUserAsync(request);

        // Assert
        result.Should().NotBeNull();
        result.Email.Should().Be("<EMAIL>");
        result.PhoneNumber.Should().Be("+************");
        result.Role.Should().Be("personal");
        result.IsVerified.Should().BeFalse();
        result.IsActive.Should().BeTrue();

        // Verify password is hashed
        result.PasswordHash.Should().NotBe("SecurePassword123!");
        BCrypt.Net.BCrypt.Verify("SecurePassword123!", result.PasswordHash).Should().BeTrue();
    }

    [Fact]
    public async Task CreateUserAsync_DuplicateEmail_ShouldThrowException()
    {
        // Arrange
        var request1 = new SignupRequest("<EMAIL>", "Password123!", "+************", "personal");
        var request2 = new SignupRequest("<EMAIL>", "Password456!", "+254712345679", "merchant");

        await _userService.CreateUserAsync(request1);

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _userService.CreateUserAsync(request2));
    }

    [Theory]
    [InlineData("invalid-role")]
    [InlineData("admin")]
    [InlineData("")]
    public async Task CreateUserAsync_InvalidRole_ShouldThrowException(string invalidRole)
    {
        // Arrange
        var request = new SignupRequest("<EMAIL>", "Password123!", "+************", invalidRole);

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _userService.CreateUserAsync(request));
    }

    [Fact]
    public async Task ValidatePasswordAsync_ValidCredentials_ShouldReturnTrue()
    {
        // Arrange
        var request = new SignupRequest("<EMAIL>", "Password123!", "+************", "personal");
        await _userService.CreateUserAsync(request);

        // Act
        var result = await _userService.ValidatePasswordAsync("<EMAIL>", "Password123!");

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task ValidatePasswordAsync_InvalidPassword_ShouldReturnFalse()
    {
        // Arrange
        var request = new SignupRequest("<EMAIL>", "Password123!", "+************", "personal");
        await _userService.CreateUserAsync(request);

        // Act
        var result = await _userService.ValidatePasswordAsync("<EMAIL>", "WrongPassword");

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task ValidatePasswordAsync_NonExistentUser_ShouldReturnFalse()
    {
        // Act
        var result = await _userService.ValidatePasswordAsync("<EMAIL>", "Password123!");

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task GetUserByEmailAsync_ExistingUser_ShouldReturnUser()
    {
        // Arrange
        var request = new SignupRequest("<EMAIL>", "Password123!", "+************", "personal");
        var createdUser = await _userService.CreateUserAsync(request);

        // Act
        var result = await _userService.GetUserByEmailAsync("<EMAIL>");

        // Assert
        result.Should().NotBeNull();
        result!.Id.Should().Be(createdUser.Id);
        result.Email.Should().Be("<EMAIL>");
    }

    [Fact]
    public async Task GetUserByEmailAsync_NonExistentUser_ShouldReturnNull()
    {
        // Act
        var result = await _userService.GetUserByEmailAsync("<EMAIL>");

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task UpdateUserVerificationAsync_ExistingUser_ShouldUpdateVerification()
    {
        // Arrange
        var request = new SignupRequest("<EMAIL>", "Password123!", "+************", "personal");
        await _userService.CreateUserAsync(request);

        // Act
        var result = await _userService.UpdateUserVerificationAsync("<EMAIL>", true);

        // Assert
        result.Should().BeTrue();

        var user = await _userService.GetUserByEmailAsync("<EMAIL>");
        user!.IsVerified.Should().BeTrue();
    }

    [Fact]
    public async Task UpdateUserVerificationAsync_NonExistentUser_ShouldReturnFalse()
    {
        // Act
        var result = await _userService.UpdateUserVerificationAsync("<EMAIL>", true);

        // Assert
        result.Should().BeFalse();
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
