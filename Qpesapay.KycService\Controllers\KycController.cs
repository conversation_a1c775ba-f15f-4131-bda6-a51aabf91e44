using Microsoft.AspNetCore.Mvc;
using Qpesapay.KycService.Models;
using Qpesapay.KycService.Services;

namespace Qpesapay.KycService.Controllers;

[ApiController]
[Route("api/[controller]")]
public class KycController : ControllerBase
{
    private readonly IKycService _kycService;
    private readonly IDocumentService _documentService;
    private readonly ILogger<KycController> _logger;

    public KycController(
        IKycService kycService,
        IDocumentService documentService,
        ILogger<KycController> logger)
    {
        _kycService = kycService;
        _documentService = documentService;
        _logger = logger;
    }

    [HttpGet("profile/{userId:guid}")]
    public async Task<IActionResult> GetProfile(Guid userId)
    {
        try
        {
            var profile = await _kycService.GetProfileByUserIdAsync(userId);
            if (profile == null)
            {
                return NotFound(new { error = "KYC profile not found" });
            }

            var response = await _kycService.MapToResponseAsync(profile);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving KYC profile for user: {UserId}", userId);
            return StatusCode(500, new { error = "An error occurred while retrieving the profile" });
        }
    }

    [HttpPost("profile")]
    public async Task<IActionResult> CreateProfile([FromBody] CreateKycProfileRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var profile = await _kycService.CreateProfileAsync(request);
            var response = await _kycService.MapToResponseAsync(profile);

            return CreatedAtAction(nameof(GetProfile), new { userId = profile.UserId }, response);
        }
        catch (InvalidOperationException ex)
        {
            return Conflict(new { error = ex.Message });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating KYC profile for user: {UserId}", request.UserId);
            return StatusCode(500, new { error = "An error occurred while creating the profile" });
        }
    }

    [HttpPut("profile/{profileId:guid}")]
    public async Task<IActionResult> UpdateProfile(Guid profileId, [FromBody] UpdateKycProfileRequest request)
    {
        try
        {
            var profile = await _kycService.UpdateProfileAsync(profileId, request);
            if (profile == null)
            {
                return NotFound(new { error = "Profile not found" });
            }

            var response = await _kycService.MapToResponseAsync(profile);
            return Ok(response);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating KYC profile: {ProfileId}", profileId);
            return StatusCode(500, new { error = "An error occurred while updating the profile" });
        }
    }

    [HttpPost("profile/{profileId:guid}/verify")]
    public async Task<IActionResult> UpdateVerificationStatus(
        Guid profileId, 
        [FromBody] VerificationStatusUpdate request)
    {
        try
        {
            var updated = await _kycService.UpdateVerificationStatusAsync(profileId, request.Status, request.Notes);
            if (!updated)
            {
                return NotFound(new { error = "Profile not found" });
            }

            return Ok(new { message = "Verification status updated successfully" });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating verification status for profile: {ProfileId}", profileId);
            return StatusCode(500, new { error = "An error occurred while updating verification status" });
        }
    }

    [HttpGet("profiles/status/{status}")]
    public async Task<IActionResult> GetProfilesByStatus(string status)
    {
        try
        {
            var profiles = await _kycService.GetProfilesByStatusAsync(status);
            var responses = new List<KycProfileResponse>();

            foreach (var profile in profiles)
            {
                var response = await _kycService.MapToResponseAsync(profile);
                responses.Add(response);
            }

            return Ok(new { profiles = responses, count = responses.Count });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving profiles by status: {Status}", status);
            return StatusCode(500, new { error = "An error occurred while retrieving profiles" });
        }
    }

    [HttpPost("upload-document")]
    public async Task<IActionResult> UploadDocument([FromForm] DocumentUploadRequest request, IFormFile file)
    {
        try
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest(new { error = "File is required" });
            }

            var document = await _documentService.UploadDocumentAsync(request.ProfileId, request.DocumentType, file);
            
            var response = new KycDocumentResponse(
                document.Id,
                document.DocumentType,
                document.FilePath,
                document.FileSize,
                document.MimeType,
                document.VerificationStatus,
                document.VerificationNotes,
                document.UploadedAt,
                document.VerifiedAt
            );

            return Ok(response);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading document for profile: {ProfileId}", request.ProfileId);
            return StatusCode(500, new { error = "An error occurred while uploading the document" });
        }
    }

    [HttpGet("document/{documentId:guid}")]
    public async Task<IActionResult> GetDocument(Guid documentId)
    {
        try
        {
            var document = await _documentService.GetDocumentAsync(documentId);
            if (document == null)
            {
                return NotFound(new { error = "Document not found" });
            }

            var response = new KycDocumentResponse(
                document.Id,
                document.DocumentType,
                document.FilePath,
                document.FileSize,
                document.MimeType,
                document.VerificationStatus,
                document.VerificationNotes,
                document.UploadedAt,
                document.VerifiedAt
            );

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving document: {DocumentId}", documentId);
            return StatusCode(500, new { error = "An error occurred while retrieving the document" });
        }
    }

    [HttpPost("document/{documentId:guid}/verify")]
    public async Task<IActionResult> VerifyDocument(
        Guid documentId, 
        [FromBody] VerificationStatusUpdate request)
    {
        try
        {
            var updated = await _documentService.UpdateDocumentVerificationAsync(
                documentId, request.Status, request.Notes);
            
            if (!updated)
            {
                return NotFound(new { error = "Document not found" });
            }

            return Ok(new { message = "Document verification status updated successfully" });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error verifying document: {DocumentId}", documentId);
            return StatusCode(500, new { error = "An error occurred while verifying the document" });
        }
    }

    [HttpDelete("document/{documentId:guid}")]
    public async Task<IActionResult> DeleteDocument(Guid documentId)
    {
        try
        {
            var deleted = await _documentService.DeleteDocumentAsync(documentId);
            if (!deleted)
            {
                return NotFound(new { error = "Document not found" });
            }

            return Ok(new { message = "Document deleted successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting document: {DocumentId}", documentId);
            return StatusCode(500, new { error = "An error occurred while deleting the document" });
        }
    }

    [HttpGet("profile/{profileId:guid}/documents")]
    public async Task<IActionResult> GetProfileDocuments(Guid profileId)
    {
        try
        {
            var documents = await _documentService.GetDocumentsByProfileAsync(profileId);
            
            var responses = documents.Select(d => new KycDocumentResponse(
                d.Id,
                d.DocumentType,
                d.FilePath,
                d.FileSize,
                d.MimeType,
                d.VerificationStatus,
                d.VerificationNotes,
                d.UploadedAt,
                d.VerifiedAt
            )).ToList();

            return Ok(new { documents = responses, count = responses.Count });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving documents for profile: {ProfileId}", profileId);
            return StatusCode(500, new { error = "An error occurred while retrieving documents" });
        }
    }
}
